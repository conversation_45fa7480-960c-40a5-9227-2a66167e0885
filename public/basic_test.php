<?php
/**
 * 最基础的测试 - 不涉及文件上传
 */

// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置内容类型
header('Content-Type: text/plain; charset=utf-8');

echo "=== Basic PHP Test ===\n";
echo "Time: " . date('Y-m-d H:i:s') . "\n";
echo "PHP Version: " . PHP_VERSION . "\n";
echo "Request Method: " . ($_SERVER['REQUEST_METHOD'] ?? 'unknown') . "\n";
echo "Script Name: " . ($_SERVER['SCRIPT_NAME'] ?? 'unknown') . "\n";

// 测试基本功能
echo "\n=== Function Tests ===\n";
echo "file_get_contents: " . (function_exists('file_get_contents') ? 'OK' : 'MISSING') . "\n";
echo "file_put_contents: " . (function_exists('file_put_contents') ? 'OK' : 'MISSING') . "\n";
echo "move_uploaded_file: " . (function_exists('move_uploaded_file') ? 'OK' : 'MISSING') . "\n";
echo "pathinfo: " . (function_exists('pathinfo') ? 'OK' : 'MISSING') . "\n";
echo "json_encode: " . (function_exists('json_encode') ? 'OK' : 'MISSING') . "\n";

// 测试扩展
echo "\n=== Extension Tests ===\n";
$extensions = ['fileinfo', 'gd', 'json', 'mbstring', 'openssl'];
foreach ($extensions as $ext) {
    echo "$ext: " . (extension_loaded($ext) ? 'OK' : 'MISSING') . "\n";
}

// 测试文件操作
echo "\n=== File Operation Tests ===\n";
$testFile = 'basic_test_' . uniqid() . '.txt';
$testContent = 'Test content: ' . date('Y-m-d H:i:s');

try {
    // 写入测试
    $writeResult = file_put_contents($testFile, $testContent);
    echo "Write test: " . ($writeResult !== false ? 'OK' : 'FAILED') . "\n";
    
    if ($writeResult !== false) {
        // 读取测试
        $readContent = file_get_contents($testFile);
        echo "Read test: " . ($readContent === $testContent ? 'OK' : 'FAILED') . "\n";
        
        // 删除测试文件
        unlink($testFile);
        echo "Delete test: OK\n";
    }
} catch (Exception $e) {
    echo "File operation error: " . $e->getMessage() . "\n";
}

// 测试目录操作
echo "\n=== Directory Tests ===\n";
$testDir = 'basic_test_dir_' . uniqid();
try {
    $mkdirResult = mkdir($testDir, 0755);
    echo "Create directory: " . ($mkdirResult ? 'OK' : 'FAILED') . "\n";
    
    if ($mkdirResult) {
        echo "Directory writable: " . (is_writable($testDir) ? 'OK' : 'FAILED') . "\n";
        rmdir($testDir);
        echo "Remove directory: OK\n";
    }
} catch (Exception $e) {
    echo "Directory operation error: " . $e->getMessage() . "\n";
}

// 测试$_FILES（如果是POST请求）
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "\n=== POST Request Tests ===\n";
    echo "FILES count: " . count($_FILES) . "\n";
    echo "POST count: " . count($_POST) . "\n";
    echo "Input size: " . strlen(file_get_contents('php://input')) . " bytes\n";
    
    if (!empty($_FILES)) {
        echo "FILES data: " . json_encode($_FILES, JSON_PRETTY_PRINT) . "\n";
    }
}

echo "\n=== Test Complete ===\n";
echo "If you see this message, basic PHP functionality is working.\n";
?>
