<?php
/**
 * 服务器诊断工具
 */

// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

header('Content-Type: application/json');

$diagnosis = [];
$diagnosis['timestamp'] = date('Y-m-d H:i:s');
$diagnosis['php_version'] = PHP_VERSION;
$diagnosis['server_software'] = $_SERVER['SERVER_SOFTWARE'] ?? 'unknown';

try {
    // 1. 基础PHP信息
    $diagnosis['php_info'] = [
        'version' => PHP_VERSION,
        'sapi' => php_sapi_name(),
        'memory_limit' => ini_get('memory_limit'),
        'max_execution_time' => ini_get('max_execution_time'),
        'error_reporting' => ini_get('error_reporting'),
        'display_errors' => ini_get('display_errors'),
        'log_errors' => ini_get('log_errors'),
        'error_log' => ini_get('error_log')
    ];

    // 2. 文件上传配置
    $diagnosis['upload_config'] = [
        'file_uploads' => ini_get('file_uploads') ? 'enabled' : 'disabled',
        'upload_max_filesize' => ini_get('upload_max_filesize'),
        'post_max_size' => ini_get('post_max_size'),
        'max_file_uploads' => ini_get('max_file_uploads'),
        'upload_tmp_dir' => ini_get('upload_tmp_dir') ?: sys_get_temp_dir()
    ];

    // 3. 目录权限检查
    $directories = [
        'public' => '.',
        'lottery' => 'lottery',
        'lottery/banner' => 'lottery/banner',
        'lottery/banner/today' => 'lottery/banner/' . date('Ymd')
    ];

    $diagnosis['directory_check'] = [];
    foreach ($directories as $name => $path) {
        $diagnosis['directory_check'][$name] = [
            'path' => $path,
            'exists' => is_dir($path),
            'readable' => is_readable($path),
            'writable' => is_writable($path),
            'permissions' => is_dir($path) ? substr(sprintf('%o', fileperms($path)), -4) : 'N/A'
        ];
    }

    // 4. 临时目录检查
    $tmpDir = sys_get_temp_dir();
    $diagnosis['temp_directory'] = [
        'path' => $tmpDir,
        'exists' => is_dir($tmpDir),
        'writable' => is_writable($tmpDir),
        'permissions' => is_dir($tmpDir) ? substr(sprintf('%o', fileperms($tmpDir)), -4) : 'N/A'
    ];

    // 5. 请求信息
    $diagnosis['request_info'] = [
        'method' => $_SERVER['REQUEST_METHOD'] ?? 'unknown',
        'content_type' => $_SERVER['CONTENT_TYPE'] ?? 'unknown',
        'content_length' => $_SERVER['CONTENT_LENGTH'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
        'remote_addr' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
    ];

    // 6. 如果是POST请求，检查上传的文件
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $diagnosis['post_data'] = [
            'files_count' => count($_FILES),
            'files_info' => $_FILES,
            'post_size' => strlen(file_get_contents('php://input')),
            'max_post_size_bytes' => parseSize(ini_get('post_max_size'))
        ];
    }

    // 7. 扩展检查
    $required_extensions = ['fileinfo', 'gd', 'json'];
    $diagnosis['extensions'] = [];
    foreach ($required_extensions as $ext) {
        $diagnosis['extensions'][$ext] = extension_loaded($ext);
    }

    // 8. 磁盘空间检查
    $diagnosis['disk_space'] = [
        'free_bytes' => disk_free_space('.'),
        'total_bytes' => disk_total_space('.'),
        'free_mb' => round(disk_free_space('.') / 1024 / 1024, 2),
        'total_mb' => round(disk_total_space('.') / 1024 / 1024, 2)
    ];

    // 9. 尝试创建测试文件
    $testFile = 'test_write_' . uniqid() . '.txt';
    $diagnosis['write_test'] = [
        'attempted' => true,
        'success' => false,
        'error' => null
    ];

    try {
        if (file_put_contents($testFile, 'test content')) {
            $diagnosis['write_test']['success'] = true;
            unlink($testFile); // 清理测试文件
        }
    } catch (Exception $e) {
        $diagnosis['write_test']['error'] = $e->getMessage();
    }

    // 10. 错误日志位置
    $diagnosis['error_logs'] = [
        'php_error_log' => ini_get('error_log'),
        'possible_locations' => [
            '/var/log/php/error.log',
            '/var/log/apache2/error.log',
            '/var/log/nginx/error.log',
            '/var/log/httpd/error_log'
        ]
    ];

    echo json_encode([
        'status' => 'success',
        'diagnosis' => $diagnosis
    ], JSON_PRETTY_PRINT);

} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'partial_diagnosis' => $diagnosis
    ], JSON_PRETTY_PRINT);
} catch (Error $e) {
    echo json_encode([
        'status' => 'fatal_error',
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'partial_diagnosis' => $diagnosis
    ], JSON_PRETTY_PRINT);
}

// 辅助函数：解析大小字符串为字节
function parseSize($size) {
    $unit = preg_replace('/[^bkmgtpezy]/i', '', $size);
    $size = preg_replace('/[^0-9\.]/', '', $size);
    if ($unit) {
        return round($size * pow(1024, stripos('bkmgtpezy', $unit[0])));
    } else {
        return round($size);
    }
}
?>
