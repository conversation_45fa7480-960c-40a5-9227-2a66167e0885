<?php
/**
 * 最小化上传测试
 */

// 开启所有错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// 记录到文件
$logFile = 'minimal_upload.log';
$log = [];

function writeLog($message) {
    global $log, $logFile;
    $log[] = date('Y-m-d H:i:s') . ': ' . $message;
    file_put_contents($logFile, implode("\n", $log) . "\n");
}

writeLog("=== Minimal Upload Test Start ===");
writeLog("PHP Version: " . PHP_VERSION);
writeLog("Request Method: " . ($_SERVER['REQUEST_METHOD'] ?? 'unknown'));

try {
    // 设置内容类型
    header('Content-Type: text/plain');
    
    writeLog("Headers set");
    
    // 检查请求方法
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        writeLog("Not a POST request");
        echo "Error: Not a POST request\n";
        echo "Method: " . ($_SERVER['REQUEST_METHOD'] ?? 'unknown') . "\n";
        exit;
    }
    
    writeLog("POST request confirmed");
    
    // 检查文件上传
    writeLog("FILES array: " . json_encode($_FILES));
    
    if (empty($_FILES)) {
        writeLog("No files uploaded");
        echo "Error: No files uploaded\n";
        echo "POST data size: " . strlen(file_get_contents('php://input')) . " bytes\n";
        echo "Content-Type: " . ($_SERVER['CONTENT_TYPE'] ?? 'unknown') . "\n";
        echo "Content-Length: " . ($_SERVER['CONTENT_LENGTH'] ?? 'unknown') . "\n";
        exit;
    }
    
    writeLog("Files found: " . count($_FILES));
    
    if (!isset($_FILES['file'])) {
        writeLog("No 'file' field found");
        echo "Error: No 'file' field found\n";
        echo "Available fields: " . implode(', ', array_keys($_FILES)) . "\n";
        exit;
    }
    
    $file = $_FILES['file'];
    writeLog("File info: " . json_encode($file));
    
    // 检查上传错误
    if ($file['error'] !== UPLOAD_ERR_OK) {
        $errorMessages = [
            UPLOAD_ERR_INI_SIZE => 'File exceeds upload_max_filesize',
            UPLOAD_ERR_FORM_SIZE => 'File exceeds MAX_FILE_SIZE',
            UPLOAD_ERR_PARTIAL => 'File was only partially uploaded',
            UPLOAD_ERR_NO_FILE => 'No file was uploaded',
            UPLOAD_ERR_NO_TMP_DIR => 'Missing temporary folder',
            UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk',
            UPLOAD_ERR_EXTENSION => 'File upload stopped by extension'
        ];
        
        $errorMsg = $errorMessages[$file['error']] ?? 'Unknown upload error';
        writeLog("Upload error: " . $file['error'] . " - " . $errorMsg);
        echo "Upload Error: " . $errorMsg . " (Code: " . $file['error'] . ")\n";
        exit;
    }
    
    writeLog("Upload error check passed");
    
    // 检查临时文件
    if (!file_exists($file['tmp_name'])) {
        writeLog("Temporary file does not exist: " . $file['tmp_name']);
        echo "Error: Temporary file does not exist\n";
        echo "Temp file path: " . $file['tmp_name'] . "\n";
        exit;
    }
    
    writeLog("Temporary file exists: " . $file['tmp_name']);
    writeLog("Temporary file size: " . filesize($file['tmp_name']));
    
    // 创建目标目录
    $targetDir = 'minimal_test/';
    if (!is_dir($targetDir)) {
        writeLog("Creating directory: " . $targetDir);
        if (!mkdir($targetDir, 0755, true)) {
            writeLog("Failed to create directory");
            echo "Error: Failed to create directory\n";
            exit;
        }
        writeLog("Directory created successfully");
    } else {
        writeLog("Directory already exists");
    }
    
    // 生成目标文件名
    $targetFile = $targetDir . 'test_' . date('YmdHis') . '_' . uniqid() . '.tmp';
    writeLog("Target file: " . $targetFile);
    
    // 移动文件
    if (move_uploaded_file($file['tmp_name'], $targetFile)) {
        writeLog("File moved successfully");
        writeLog("Final file size: " . filesize($targetFile));
        
        echo "SUCCESS!\n";
        echo "File uploaded successfully\n";
        echo "Original name: " . $file['name'] . "\n";
        echo "Size: " . $file['size'] . " bytes\n";
        echo "Target file: " . $targetFile . "\n";
        echo "Final size: " . filesize($targetFile) . " bytes\n";
        
    } else {
        writeLog("Failed to move file");
        echo "Error: Failed to move uploaded file\n";
        echo "Source: " . $file['tmp_name'] . "\n";
        echo "Target: " . $targetFile . "\n";
        echo "Target dir writable: " . (is_writable($targetDir) ? 'yes' : 'no') . "\n";
    }
    
} catch (Exception $e) {
    writeLog("Exception: " . $e->getMessage());
    writeLog("File: " . $e->getFile());
    writeLog("Line: " . $e->getLine());
    
    echo "Exception: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
    
} catch (Error $e) {
    writeLog("Fatal Error: " . $e->getMessage());
    writeLog("File: " . $e->getFile());
    writeLog("Line: " . $e->getLine());
    
    echo "Fatal Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}

writeLog("=== Minimal Upload Test End ===");
?>
