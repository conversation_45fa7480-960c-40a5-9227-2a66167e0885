<!DOCTYPE html>
<html>
<head>
    <title>服务器诊断工具</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        button { padding: 10px 20px; margin: 10px 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
        .result { margin: 10px 0; padding: 15px; background: #f5f5f5; border-radius: 3px; white-space: pre-wrap; font-family: monospace; max-height: 400px; overflow-y: auto; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        .warning { background: #fff3e0; color: #ef6c00; }
        input[type="file"] { margin: 10px 0; }
        h2 { color: #333; border-bottom: 2px solid #007cba; padding-bottom: 5px; }
        .step { margin: 10px 0; padding: 10px; background: #f0f8ff; border-left: 4px solid #007cba; }
    </style>
</head>
<body>
    <h1>🔧 服务器诊断工具</h1>
    <p>这个工具可以帮助诊断服务器上传功能的问题。请按顺序执行以下步骤：</p>

    <div class="section">
        <h2>步骤 1: 服务器环境诊断</h2>
        <div class="step">首先检查服务器的基本配置和环境</div>
        <button onclick="runDiagnosis()">🔍 运行服务器诊断</button>
        <div id="diagnosis-result" class="result" style="display:none;"></div>
    </div>

    <div class="section">
        <h2>步骤 2: 最小化上传测试</h2>
        <div class="step">使用最简单的PHP代码测试文件上传功能</div>
        <input type="file" id="minimal-file" accept="*/*">
        <br>
        <button onclick="testMinimal()">📤 最小化上传测试</button>
        <div id="minimal-result" class="result" style="display:none;"></div>
    </div>

    <div class="section">
        <h2>步骤 3: 直接上传测试</h2>
        <div class="step">测试不通过ThinkPHP框架的直接上传</div>
        <input type="file" id="direct-file" accept="image/*">
        <br>
        <button onclick="testDirect()">📤 直接上传测试</button>
        <div id="direct-result" class="result" style="display:none;"></div>
    </div>

    <div class="section">
        <h2>步骤 4: Banner上传测试</h2>
        <div class="step">测试通过ThinkPHP框架的Banner上传</div>
        <input type="file" id="banner-file" accept="image/*">
        <br>
        <button onclick="testBanner()">📤 Banner上传测试</button>
        <div id="banner-result" class="result" style="display:none;"></div>
    </div>

    <div class="section">
        <h2>步骤 5: 查看日志</h2>
        <div class="step">查看详细的调试日志信息</div>
        <button onclick="viewLog('minimal_upload.log', 'log-result')">📋 最小化测试日志</button>
        <button onclick="viewLog('debug_direct_upload.log', 'log-result')">📋 直接上传日志</button>
        <button onclick="viewLog('debug_banner_upload.log', 'log-result')">📋 Banner上传日志</button>
        <div id="log-result" class="result" style="display:none;"></div>
    </div>

    <script>
        function showResult(elementId, content, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = 'result ' + type;
            element.innerHTML = content;
            element.style.display = 'block';
        }

        function runDiagnosis() {
            showResult('diagnosis-result', '🔄 正在运行诊断...', 'warning');
            
            fetch('/server_diagnosis.php')
                .then(response => response.text())
                .then(text => {
                    try {
                        const data = JSON.parse(text);
                        if (data.status === 'success') {
                            showResult('diagnosis-result', '✅ 诊断完成:\n\n' + JSON.stringify(data.diagnosis, null, 2), 'success');
                        } else {
                            showResult('diagnosis-result', '❌ 诊断出现问题:\n\n' + JSON.stringify(data, null, 2), 'error');
                        }
                    } catch (e) {
                        showResult('diagnosis-result', '❌ 诊断响应解析失败:\n\n' + text, 'error');
                    }
                })
                .catch(error => {
                    showResult('diagnosis-result', '❌ 诊断请求失败: ' + error.message, 'error');
                });
        }

        function testMinimal() {
            const fileInput = document.getElementById('minimal-file');
            if (!fileInput.files[0]) {
                alert('请选择文件');
                return;
            }
            
            showResult('minimal-result', '🔄 正在上传...', 'warning');
            
            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            
            fetch('/minimal_upload.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(text => {
                const type = text.includes('SUCCESS') ? 'success' : 'error';
                const icon = type === 'success' ? '✅' : '❌';
                showResult('minimal-result', icon + ' 最小化上传结果:\n\n' + text, type);
            })
            .catch(error => {
                showResult('minimal-result', '❌ 请求失败: ' + error.message, 'error');
            });
        }

        function testDirect() {
            const fileInput = document.getElementById('direct-file');
            if (!fileInput.files[0]) {
                alert('请选择图片文件');
                return;
            }
            
            showResult('direct-result', '🔄 正在上传...', 'warning');
            
            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            
            fetch('/direct_upload_test.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(text => {
                try {
                    const data = JSON.parse(text);
                    const type = data.code === 200 ? 'success' : 'error';
                    const icon = type === 'success' ? '✅' : '❌';
                    showResult('direct-result', icon + ' 直接上传结果:\n\n' + JSON.stringify(data, null, 2), type);
                } catch (e) {
                    showResult('direct-result', '❌ 响应解析失败:\n\n' + text, 'error');
                }
            })
            .catch(error => {
                showResult('direct-result', '❌ 请求失败: ' + error.message, 'error');
            });
        }

        function testBanner() {
            const fileInput = document.getElementById('banner-file');
            if (!fileInput.files[0]) {
                alert('请选择图片文件');
                return;
            }
            
            showResult('banner-result', '🔄 正在上传...', 'warning');
            
            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            
            fetch('/admin/banner/doupload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(text => {
                try {
                    const data = JSON.parse(text);
                    const type = data.code === 200 ? 'success' : 'error';
                    const icon = type === 'success' ? '✅' : '❌';
                    showResult('banner-result', icon + ' Banner上传结果:\n\n' + JSON.stringify(data, null, 2), type);
                } catch (e) {
                    showResult('banner-result', '❌ 响应解析失败:\n\n' + text, 'error');
                }
            })
            .catch(error => {
                showResult('banner-result', '❌ 请求失败: ' + error.message, 'error');
            });
        }

        function viewLog(logFile, resultId) {
            showResult(resultId, '🔄 正在读取日志...', 'warning');
            
            fetch('/' + logFile)
                .then(response => response.text())
                .then(text => {
                    if (text.trim()) {
                        showResult(resultId, '📋 ' + logFile + ' 内容:\n\n' + text, 'info');
                    } else {
                        showResult(resultId, '📋 ' + logFile + ' 暂无内容', 'warning');
                    }
                })
                .catch(error => {
                    showResult(resultId, '❌ 无法读取 ' + logFile + ': ' + error.message, 'error');
                });
        }
    </script>
</body>
</html>
