<!DOCTYPE html>
<html>
<head>
    <title>Banner上传测试</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
        button { padding: 10px 20px; margin: 10px 0; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; white-space: pre-wrap; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        input[type="file"] { margin: 10px 0; }
    </style>
</head>
<body>
    <h1>Banner上传测试</h1>
    
    <div class="test-section">
        <h2>1. 直接上传测试（不通过ThinkPHP）</h2>
        <input type="file" id="direct-file" accept="image/*">
        <br>
        <button onclick="testDirectUpload()">测试直接上传</button>
        <div id="direct-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>2. Banner上传测试（通过ThinkPHP）</h2>
        <input type="file" id="banner-file" accept="image/*">
        <br>
        <button onclick="testBannerUpload()">测试Banner上传</button>
        <div id="banner-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 查看调试日志</h2>
        <button onclick="viewDebugLog()">查看Banner调试日志</button>
        <button onclick="viewDirectDebugLog()">查看直接上传调试日志</button>
        <div id="debug-log" class="result"></div>
    </div>

    <script>
        function testDirectUpload() {
            const fileInput = document.getElementById('direct-file');
            if (!fileInput.files[0]) {
                alert('请选择文件');
                return;
            }

            const formData = new FormData();
            formData.append('file', fileInput.files[0]);

            document.getElementById('direct-result').innerHTML = '上传中...';

            fetch('/direct_upload_test.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('Direct Response status:', response.status);
                return response.text();
            })
            .then(text => {
                console.log('Direct Response text:', text);
                try {
                    const data = JSON.parse(text);
                    const resultClass = data.code === 200 ? 'success' : 'error';
                    document.getElementById('direct-result').innerHTML =
                        '<div class="' + resultClass + '">结果: ' + JSON.stringify(data, null, 2) + '</div>';
                } catch (e) {
                    document.getElementById('direct-result').innerHTML =
                        '<div class="error">响应解析失败:\n' + text + '</div>';
                }
            })
            .catch(error => {
                console.error('Direct Fetch error:', error);
                document.getElementById('direct-result').innerHTML =
                    '<div class="error">请求失败: ' + error.message + '</div>';
            });
        }

        function testBannerUpload() {
            const fileInput = document.getElementById('banner-file');
            if (!fileInput.files[0]) {
                alert('请选择文件');
                return;
            }
            
            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            
            document.getElementById('banner-result').innerHTML = '上传中...';
            
            fetch('/admin/banner/doupload', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                return response.text();
            })
            .then(text => {
                console.log('Response text:', text);
                try {
                    const data = JSON.parse(text);
                    const resultClass = data.code === 200 ? 'success' : 'error';
                    document.getElementById('banner-result').innerHTML = 
                        '<div class="' + resultClass + '">结果: ' + JSON.stringify(data, null, 2) + '</div>';
                } catch (e) {
                    document.getElementById('banner-result').innerHTML = 
                        '<div class="error">响应解析失败:\n' + text + '</div>';
                }
            })
            .catch(error => {
                console.error('Fetch error:', error);
                document.getElementById('banner-result').innerHTML = 
                    '<div class="error">请求失败: ' + error.message + '</div>';
            });
        }
        
        function viewDebugLog() {
            fetch('/debug_banner_upload.log')
                .then(response => response.text())
                .then(text => {
                    document.getElementById('debug-log').innerHTML = '<h4>Banner调试日志:</h4>' + (text || '暂无日志');
                })
                .catch(error => {
                    document.getElementById('debug-log').innerHTML = '无法读取Banner日志: ' + error.message;
                });
        }

        function viewDirectDebugLog() {
            fetch('/debug_direct_upload.log')
                .then(response => response.text())
                .then(text => {
                    document.getElementById('debug-log').innerHTML = '<h4>直接上传调试日志:</h4>' + (text || '暂无日志');
                })
                .catch(error => {
                    document.getElementById('debug-log').innerHTML = '无法读取直接上传日志: ' + error.message;
                });
        }
    </script>
</body>
</html>
