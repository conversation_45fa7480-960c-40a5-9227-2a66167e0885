<?php
/**
 * 禁用OSS配置工具
 */

// 设置错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json; charset=utf-8');

try {
    // 引入ThinkPHP框架
    require_once '../thinkphp/start.php';
    
    // 获取SystemModel
    $systemModel = new \app\admin\model\SystemModel();
    
    // 检查当前OSS配置
    $currentConfig = $systemModel->getConfig('oss');
    
    if (empty($currentConfig)) {
        echo json_encode([
            'status' => 'info',
            'message' => 'OSS配置不存在，无需禁用',
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 禁用OSS配置
    $disabledConfig = $currentConfig;
    $disabledConfig['enable'] = false;
    
    // 保存配置
    $result = $systemModel->saveData($disabledConfig, 'oss');
    
    if ($result) {
        echo json_encode([
            'status' => 'success',
            'message' => 'OSS配置已成功禁用，现在将使用本地上传',
            'timestamp' => date('Y-m-d H:i:s'),
            'previous_config' => $currentConfig,
            'new_config' => $disabledConfig
        ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    } else {
        echo json_encode([
            'status' => 'error',
            'message' => 'OSS配置禁用失败',
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
} catch (Error $e) {
    echo json_encode([
        'status' => 'fatal_error',
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
}
?>
