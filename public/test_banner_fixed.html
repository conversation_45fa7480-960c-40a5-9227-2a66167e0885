<!DOCTYPE html>
<html>
<head>
    <title>Banner上传修复测试</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        button { padding: 10px 20px; margin: 10px 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
        .result { margin: 10px 0; padding: 15px; background: #f5f5f5; border-radius: 3px; white-space: pre-wrap; font-family: monospace; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        input[type="file"] { margin: 10px 0; }
        h2 { color: #333; }
    </style>
</head>
<body>
    <h1>🎯 Banner上传修复测试</h1>
    <p>现在Banner控制器已经修复为使用正确的OssModel方法，与System控制器保持一致。</p>

    <div class="section">
        <h2>Banner上传测试（已修复）</h2>
        <p>使用修复后的Banner上传接口：<code>/admin/banner/doupload</code></p>
        <input type="file" id="banner-file" accept="image/*">
        <br>
        <button onclick="testBannerUpload()">🚀 测试Banner上传</button>
        <div id="banner-result" class="result" style="display:none;"></div>
    </div>

    <div class="section">
        <h2>System上传测试（对比）</h2>
        <p>使用System控制器的上传接口：<code>/admin/system/doupload</code></p>
        <input type="file" id="system-file" accept="image/*">
        <br>
        <button onclick="testSystemUpload()">🔄 测试System上传</button>
        <div id="system-result" class="result" style="display:none;"></div>
    </div>

    <div class="section">
        <h2>查看错误日志</h2>
        <p>如果上传失败，可以查看详细的错误日志</p>
        <button onclick="checkPhpErrorLog()">📋 查看PHP错误日志</button>
        <button onclick="checkNginxErrorLog()">📋 查看Nginx错误日志</button>
        <div id="log-result" class="result" style="display:none;"></div>
    </div>

    <script>
        function showResult(elementId, content, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = 'result ' + type;
            element.innerHTML = content;
            element.style.display = 'block';
        }

        function testBannerUpload() {
            const fileInput = document.getElementById('banner-file');
            if (!fileInput.files[0]) {
                alert('请选择图片文件');
                return;
            }
            
            showResult('banner-result', '🔄 正在上传...', 'warning');
            
            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            
            fetch('/admin/banner/doupload', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('Banner Response status:', response.status);
                return response.text();
            })
            .then(text => {
                console.log('Banner Response text:', text);
                try {
                    const data = JSON.parse(text);
                    const type = data.code === 200 ? 'success' : 'error';
                    const icon = type === 'success' ? '✅' : '❌';
                    showResult('banner-result', icon + ' Banner上传结果:\n\n' + JSON.stringify(data, null, 2), type);
                } catch (e) {
                    showResult('banner-result', '❌ 响应解析失败:\n\n' + text, 'error');
                }
            })
            .catch(error => {
                showResult('banner-result', '❌ 请求失败: ' + error.message, 'error');
            });
        }

        function testSystemUpload() {
            const fileInput = document.getElementById('system-file');
            if (!fileInput.files[0]) {
                alert('请选择图片文件');
                return;
            }
            
            showResult('system-result', '🔄 正在上传...', 'warning');
            
            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            
            fetch('/admin/system/doupload', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('System Response status:', response.status);
                return response.text();
            })
            .then(text => {
                console.log('System Response text:', text);
                try {
                    const data = JSON.parse(text);
                    const type = data.code === 200 ? 'success' : 'error';
                    const icon = type === 'success' ? '✅' : '❌';
                    showResult('system-result', icon + ' System上传结果:\n\n' + JSON.stringify(data, null, 2), type);
                } catch (e) {
                    showResult('system-result', '❌ 响应解析失败:\n\n' + text, 'error');
                }
            })
            .catch(error => {
                showResult('system-result', '❌ 请求失败: ' + error.message, 'error');
            });
        }

        function checkPhpErrorLog() {
            showResult('log-result', '🔄 正在读取PHP错误日志...', 'warning');
            
            // 尝试读取可能的PHP错误日志位置
            const logPaths = [
                '/var/log/php/error.log',
                '/var/log/php_errors.log',
                'error_log'
            ];
            
            // 这里只是示例，实际需要服务器端脚本来读取日志
            showResult('log-result', '📋 请在服务器上检查以下位置的PHP错误日志:\n\n' + 
                logPaths.join('\n') + '\n\n' +
                '或者运行命令: tail -f /var/log/php/error.log', 'info');
        }

        function checkNginxErrorLog() {
            showResult('log-result', '📋 请在服务器上检查Nginx错误日志:\n\n' +
                'tail -f /var/log/nginx/error.log\n\n' +
                '或者检查: /var/log/nginx/access.log', 'info');
        }
    </script>
</body>
</html>
