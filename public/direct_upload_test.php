<?php
/**
 * 直接上传测试 - 不依赖ThinkPHP框架
 */

header('Content-Type: application/json');

$debugInfo = [];
$debugInfo[] = "=== Direct Upload Test ===";
$debugInfo[] = "Time: " . date('Y-m-d H:i:s');
$debugInfo[] = "Request Method: " . ($_SERVER['REQUEST_METHOD'] ?? 'unknown');
$debugInfo[] = "PHP Version: " . PHP_VERSION;

try {
    // 检查是否有文件上传
    if (empty($_FILES['file'])) {
        $debugInfo[] = "ERROR: 没有上传文件";
        $debugInfo[] = "FILES: " . json_encode($_FILES);
        file_put_contents('debug_direct_upload.log', implode("\n", $debugInfo) . "\n", FILE_APPEND);
        echo json_encode(['code' => 401, 'msg' => '没有上传文件', 'debug' => $_FILES]);
        exit;
    }
    
    $debugInfo[] = "文件上传检查通过";
    $debugInfo[] = "FILES: " . json_encode($_FILES);
    
    // 检查上传错误
    if ($_FILES['file']['error'] !== UPLOAD_ERR_OK) {
        $debugInfo[] = "ERROR: 文件上传错误: " . $_FILES['file']['error'];
        file_put_contents('debug_direct_upload.log', implode("\n", $debugInfo) . "\n", FILE_APPEND);
        echo json_encode(['code' => 401, 'msg' => '文件上传错误: ' . $_FILES['file']['error']]);
        exit;
    }
    
    $debugInfo[] = "文件上传错误检查通过";
    
    // 检查文件类型
    $fileName = $_FILES['file']['name'];
    $extension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
    $debugInfo[] = "文件名: " . $fileName;
    $debugInfo[] = "文件扩展名: " . $extension;
    
    if (!in_array($extension, ['jpg', 'jpeg', 'png', 'gif'])) {
        $debugInfo[] = "ERROR: 不支持的文件类型: " . $extension;
        file_put_contents('debug_direct_upload.log', implode("\n", $debugInfo) . "\n", FILE_APPEND);
        echo json_encode(['code' => 401, 'msg' => '不支持的文件类型: ' . $extension]);
        exit;
    }
    
    $debugInfo[] = "文件类型检查通过";
    
    // 创建上传目录
    $uploadDir = 'lottery/banner/' . date('Ymd') . '/';
    $debugInfo[] = "上传目录: " . $uploadDir;
    $debugInfo[] = "目录是否存在: " . (is_dir($uploadDir) ? 'yes' : 'no');
    
    if (!is_dir($uploadDir)) {
        if (!mkdir($uploadDir, 0755, true)) {
            $debugInfo[] = "ERROR: 创建目录失败";
            file_put_contents('debug_direct_upload.log', implode("\n", $debugInfo) . "\n", FILE_APPEND);
            echo json_encode(['code' => 401, 'msg' => '创建目录失败']);
            exit;
        }
        $debugInfo[] = "目录创建成功";
    } else {
        $debugInfo[] = "目录已存在";
    }
    
    // 检查目录权限
    $debugInfo[] = "目录可写: " . (is_writable($uploadDir) ? 'yes' : 'no');
    
    // 生成新文件名
    $newFileName = 'direct_' . date('YmdHis') . '_' . uniqid() . '.' . $extension;
    $targetPath = $uploadDir . $newFileName;
    $debugInfo[] = "新文件名: " . $newFileName;
    $debugInfo[] = "目标路径: " . $targetPath;
    $debugInfo[] = "临时文件: " . $_FILES['file']['tmp_name'];
    $debugInfo[] = "临时文件存在: " . (file_exists($_FILES['file']['tmp_name']) ? 'yes' : 'no');
    
    // 移动文件
    if (move_uploaded_file($_FILES['file']['tmp_name'], $targetPath)) {
        $relativePath = '/lottery/banner/' . date('Ymd') . '/' . $newFileName;
        $debugInfo[] = "文件移动成功";
        $debugInfo[] = "相对路径: " . $relativePath;
        $debugInfo[] = "文件大小: " . filesize($targetPath);
        
        file_put_contents('debug_direct_upload.log', implode("\n", $debugInfo) . "\n", FILE_APPEND);
        echo json_encode([
            'code' => 200, 
            'msg' => '上传成功', 
            'data' => $relativePath,
            'debug' => [
                'target_path' => $targetPath,
                'file_size' => filesize($targetPath),
                'upload_dir' => $uploadDir
            ]
        ]);
    } else {
        $debugInfo[] = "ERROR: 文件移动失败";
        $debugInfo[] = "Last error: " . error_get_last()['message'] ?? 'unknown';
        file_put_contents('debug_direct_upload.log', implode("\n", $debugInfo) . "\n", FILE_APPEND);
        echo json_encode(['code' => 401, 'msg' => '文件移动失败']);
    }
    
} catch (Exception $e) {
    $debugInfo[] = "EXCEPTION: " . $e->getMessage();
    $debugInfo[] = "File: " . $e->getFile();
    $debugInfo[] = "Line: " . $e->getLine();
    file_put_contents('debug_direct_upload.log', implode("\n", $debugInfo) . "\n", FILE_APPEND);
    echo json_encode(['code' => 500, 'msg' => '发生异常: ' . $e->getMessage()]);
} catch (Error $e) {
    $debugInfo[] = "FATAL ERROR: " . $e->getMessage();
    $debugInfo[] = "File: " . $e->getFile();
    $debugInfo[] = "Line: " . $e->getLine();
    file_put_contents('debug_direct_upload.log', implode("\n", $debugInfo) . "\n", FILE_APPEND);
    echo json_encode(['code' => 500, 'msg' => '致命错误: ' . $e->getMessage()]);
}
?>
