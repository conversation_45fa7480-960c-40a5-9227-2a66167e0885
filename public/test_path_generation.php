<?php
/**
 * 测试路径生成逻辑
 */

header('Content-Type: application/json; charset=utf-8');

function testPathGeneration($inputPath, $fileName) {
    // 模拟OssModel中的路径生成逻辑
    
    // 确保路径以public/开头
    if (strpos($inputPath, 'public/') !== 0) {
        $path = 'public/' . ltrim($inputPath, '/');
    } else {
        $path = $inputPath;
    }
    
    // 生成相对路径URL
    $urlPath = str_replace('public/', '', $path);
    $urlPath = trim($urlPath, '/');
    
    // 确保路径格式正确，避免双斜杠
    if (empty($urlPath)) {
        $relativePath = '/' . $fileName;
    } else {
        $relativePath = '/' . $urlPath . '/' . $fileName;
    }
    
    // 清理路径中的双斜杠
    $relativePath = preg_replace('#/+#', '/', $relativePath);
    
    return [
        'input_path' => $inputPath,
        'processed_path' => $path,
        'url_path' => $urlPath,
        'file_name' => $fileName,
        'final_relative_path' => $relativePath
    ];
}

// 测试不同的路径情况
$testCases = [
    ['base/ico/', 'test.png'],
    ['public/base/ico/', 'test.png'],
    ['/base/ico/', 'test.png'],
    ['uploads/images/', 'test.jpg'],
    ['public/uploads/images/', 'test.jpg'],
    ['', 'test.gif'],
    ['public/', 'test.webp'],
    ['base/ico', 'test.png'], // 没有尾部斜杠
];

$results = [];
foreach ($testCases as $case) {
    $results[] = testPathGeneration($case[0], $case[1]);
}

echo json_encode([
    'status' => 'success',
    'timestamp' => date('Y-m-d H:i:s'),
    'test_cases' => $results,
    'notes' => [
        '路径生成逻辑测试',
        '检查是否有双斜杠或格式问题',
        '确保返回的路径可以被浏览器正确访问'
    ]
], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?>
