<!DOCTYPE html>
<html>
<head>
    <title>上传路径调试工具</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        button { padding: 10px 20px; margin: 10px 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
        .result { margin: 10px 0; padding: 15px; background: #f5f5f5; border-radius: 3px; white-space: pre-wrap; font-family: monospace; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        .warning { background: #fff3e0; color: #ef6c00; }
        input[type="file"] { margin: 10px 0; }
        h2 { color: #333; }
        .image-preview { max-width: 300px; max-height: 300px; margin: 10px 0; border: 1px solid #ddd; }
        .path-test { background: #e3f2fd; padding: 10px; margin: 10px 0; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>🔍 上传路径调试工具</h1>
    <p>测试文件上传并检查返回的路径是否可以正确访问。</p>

    <div class="section">
        <h2>文件上传测试</h2>
        <input type="file" id="test-file" accept="image/*">
        <br>
        <button onclick="testUpload()">📤 上传并测试路径</button>
        <div id="upload-result" class="result" style="display:none;"></div>
    </div>

    <div class="section">
        <h2>路径测试</h2>
        <p>手动输入路径进行测试：</p>
        <input type="text" id="manual-path" placeholder="例如：/uploads/images/test.jpg" style="width: 300px;">
        <br>
        <button onclick="testPath()">🔗 测试路径访问</button>
        <div id="path-result" class="result" style="display:none;"></div>
    </div>

    <div class="section">
        <h2>图片预览</h2>
        <div id="image-preview"></div>
    </div>

    <script>
        function showResult(elementId, content, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = 'result ' + type;
            element.innerHTML = content;
            element.style.display = 'block';
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function testImageLoad(imagePath) {
            return new Promise((resolve) => {
                const img = new Image();
                const startTime = Date.now();
                
                img.onload = function() {
                    const loadTime = Date.now() - startTime;
                    resolve({
                        success: true,
                        width: this.naturalWidth,
                        height: this.naturalHeight,
                        loadTime: loadTime
                    });
                };
                
                img.onerror = function() {
                    const loadTime = Date.now() - startTime;
                    resolve({
                        success: false,
                        loadTime: loadTime,
                        error: '图片加载失败'
                    });
                };
                
                img.src = imagePath;
            });
        }

        function testPath() {
            const path = document.getElementById('manual-path').value.trim();
            if (!path) {
                alert('请输入路径');
                return;
            }
            
            showResult('path-result', `🔄 正在测试路径: ${path}`, 'warning');
            
            testImageLoad(path).then(result => {
                if (result.success) {
                    const resultText = `✅ 路径访问成功！

📁 路径信息:
• 测试路径: ${path}
• 图片尺寸: ${result.width} x ${result.height}
• 加载时间: ${result.loadTime}ms

🖼️ 图片已在下方显示`;
                    
                    showResult('path-result', resultText, 'success');
                    
                    // 显示图片预览
                    document.getElementById('image-preview').innerHTML = 
                        `<img src="${path}" class="image-preview" alt="预览图片">`;
                } else {
                    const resultText = `❌ 路径访问失败！

📁 路径信息:
• 测试路径: ${path}
• 错误信息: ${result.error}
• 尝试时间: ${result.loadTime}ms

🔧 可能的原因:
• 文件不存在
• 路径格式错误
• 服务器配置问题
• 权限问题`;
                    
                    showResult('path-result', resultText, 'error');
                    document.getElementById('image-preview').innerHTML = '';
                }
            });
        }

        function testUpload() {
            const fileInput = document.getElementById('test-file');
            if (!fileInput.files[0]) {
                alert('请选择文件');
                return;
            }
            
            const file = fileInput.files[0];
            showResult('upload-result', `🔄 正在上传文件...
文件名: ${file.name}
文件大小: ${formatFileSize(file.size)}
文件类型: ${file.type}`, 'warning');
            
            const formData = new FormData();
            formData.append('file', file);
            
            const startTime = Date.now();
            
            fetch('/admin/banner/doupload', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                const endTime = Date.now();
                const uploadTime = ((endTime - startTime) / 1000).toFixed(2);
                
                return response.text().then(text => ({
                    status: response.status,
                    text: text,
                    uploadTime: uploadTime
                }));
            })
            .then(({status, text, uploadTime}) => {
                try {
                    const data = JSON.parse(text);
                    
                    if (data.code === 200) {
                        const returnedPath = data.data;
                        
                        showResult('upload-result', `✅ 上传成功！正在测试返回的路径...

📁 上传信息:
• 文件名: ${file.name}
• 文件大小: ${formatFileSize(file.size)}
• 上传时间: ${uploadTime}秒
• 返回路径: ${returnedPath}

🔄 正在测试路径访问...`, 'success');
                        
                        // 测试返回的路径是否可以访问
                        testImageLoad(returnedPath).then(result => {
                            let pathTestResult;
                            if (result.success) {
                                pathTestResult = `✅ 路径访问测试成功！
• 图片尺寸: ${result.width} x ${result.height}
• 加载时间: ${result.loadTime}ms`;
                                
                                // 显示图片预览
                                document.getElementById('image-preview').innerHTML = 
                                    `<img src="${returnedPath}" class="image-preview" alt="上传的图片">`;
                                    
                                // 自动填入手动测试框
                                document.getElementById('manual-path').value = returnedPath;
                            } else {
                                pathTestResult = `❌ 路径访问测试失败！
• 错误: ${result.error}
• 尝试时间: ${result.loadTime}ms

🔧 可能的问题:
• 返回的路径格式不正确
• 文件实际保存位置与返回路径不匹配
• Web服务器配置问题`;
                                
                                document.getElementById('image-preview').innerHTML = '';
                            }
                            
                            const finalResult = `✅ 上传成功！

📁 上传信息:
• 文件名: ${file.name}
• 文件大小: ${formatFileSize(file.size)}
• 上传时间: ${uploadTime}秒
• 返回路径: ${returnedPath}

🔗 路径访问测试:
${pathTestResult}

📡 完整服务器响应:
${JSON.stringify(data, null, 2)}`;
                            
                            showResult('upload-result', finalResult, result.success ? 'success' : 'warning');
                        });
                        
                    } else {
                        showResult('upload-result', `❌ 上传失败！

📁 文件信息:
• 文件名: ${file.name}
• 文件大小: ${formatFileSize(file.size)}
• 上传时间: ${uploadTime}秒

📡 服务器响应:
${JSON.stringify(data, null, 2)}`, 'error');
                    }
                } catch (e) {
                    showResult('upload-result', `❌ 响应解析失败！

📁 文件信息:
• 文件名: ${file.name}
• 文件大小: ${formatFileSize(file.size)}
• 上传时间: ${uploadTime}秒

📡 原始响应:
${text}

❌ 解析错误:
${e.message}`, 'error');
                }
            })
            .catch(error => {
                showResult('upload-result', `❌ 请求失败！

📁 文件信息:
• 文件名: ${file.name}
• 文件大小: ${formatFileSize(file.size)}

❌ 错误信息:
${error.message}`, 'error');
            });
        }
    </script>
</body>
</html>
