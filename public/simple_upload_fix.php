<?php
/**
 * 简单上传修复 - 解决fileinfo扩展问题
 */

// 开启所有错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// 记录到文件
$logFile = 'simple_upload_fix.log';

function writeLog($message) {
    global $logFile;
    $logEntry = date('Y-m-d H:i:s') . ': ' . $message . "\n";
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

writeLog("=== Simple Upload Fix Test Start ===");
writeLog("PHP Version: " . PHP_VERSION);
writeLog("Request Method: " . ($_SERVER['REQUEST_METHOD'] ?? 'unknown'));

// 检查关键扩展
$extensions = ['fileinfo', 'gd', 'json'];
foreach ($extensions as $ext) {
    $loaded = extension_loaded($ext);
    writeLog("Extension $ext: " . ($loaded ? 'loaded' : 'NOT LOADED'));
}

try {
    // 设置内容类型
    header('Content-Type: text/plain; charset=utf-8');
    
    echo "=== Simple Upload Fix Test ===\n";
    echo "Time: " . date('Y-m-d H:i:s') . "\n";
    echo "PHP Version: " . PHP_VERSION . "\n\n";
    
    // 检查扩展
    echo "Extension Check:\n";
    foreach ($extensions as $ext) {
        $loaded = extension_loaded($ext);
        echo "- $ext: " . ($loaded ? 'OK' : 'MISSING') . "\n";
    }
    echo "\n";
    
    // 检查请求方法
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        writeLog("Not a POST request");
        echo "Error: This script requires POST method\n";
        echo "Current method: " . ($_SERVER['REQUEST_METHOD'] ?? 'unknown') . "\n";
        exit;
    }
    
    writeLog("POST request confirmed");
    echo "Request method: POST ✓\n\n";
    
    // 检查文件上传
    echo "Upload Check:\n";
    echo "Files count: " . count($_FILES) . "\n";
    
    if (empty($_FILES)) {
        writeLog("No files uploaded");
        echo "Error: No files uploaded\n";
        echo "POST size: " . strlen(file_get_contents('php://input')) . " bytes\n";
        echo "Content-Type: " . ($_SERVER['CONTENT_TYPE'] ?? 'unknown') . "\n";
        echo "Content-Length: " . ($_SERVER['CONTENT_LENGTH'] ?? 'unknown') . "\n";
        exit;
    }
    
    if (!isset($_FILES['file'])) {
        writeLog("No 'file' field found");
        echo "Error: No 'file' field found\n";
        echo "Available fields: " . implode(', ', array_keys($_FILES)) . "\n";
        exit;
    }
    
    $file = $_FILES['file'];
    writeLog("File upload detected: " . $file['name']);
    
    echo "File detected: " . $file['name'] . " ✓\n";
    echo "File size: " . $file['size'] . " bytes\n";
    echo "File type (browser): " . $file['type'] . "\n";
    echo "Temp file: " . $file['tmp_name'] . "\n\n";
    
    // 检查上传错误
    if ($file['error'] !== UPLOAD_ERR_OK) {
        $errorMessages = [
            UPLOAD_ERR_INI_SIZE => 'File exceeds upload_max_filesize',
            UPLOAD_ERR_FORM_SIZE => 'File exceeds MAX_FILE_SIZE',
            UPLOAD_ERR_PARTIAL => 'File was only partially uploaded',
            UPLOAD_ERR_NO_FILE => 'No file was uploaded',
            UPLOAD_ERR_NO_TMP_DIR => 'Missing temporary folder',
            UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk',
            UPLOAD_ERR_EXTENSION => 'File upload stopped by extension'
        ];
        
        $errorMsg = $errorMessages[$file['error']] ?? 'Unknown upload error';
        writeLog("Upload error: " . $file['error'] . " - " . $errorMsg);
        echo "Upload Error: " . $errorMsg . " (Code: " . $file['error'] . ")\n";
        exit;
    }
    
    echo "Upload error check: OK ✓\n\n";
    
    // 检查临时文件
    if (!file_exists($file['tmp_name'])) {
        writeLog("Temporary file does not exist: " . $file['tmp_name']);
        echo "Error: Temporary file does not exist\n";
        exit;
    }
    
    echo "Temp file exists: OK ✓\n";
    echo "Temp file size: " . filesize($file['tmp_name']) . " bytes\n\n";
    
    // 简单的文件类型检查（不使用fileinfo）
    $fileName = $file['name'];
    $extension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
    
    echo "File Analysis:\n";
    echo "Original name: " . $fileName . "\n";
    echo "Extension: " . $extension . "\n";
    
    // 不依赖fileinfo的MIME类型检测
    $mimeTypes = [
        'jpg' => 'image/jpeg',
        'jpeg' => 'image/jpeg',
        'png' => 'image/png',
        'gif' => 'image/gif',
        'txt' => 'text/plain',
        'pdf' => 'application/pdf'
    ];
    
    $expectedMime = $mimeTypes[$extension] ?? 'unknown';
    echo "Expected MIME: " . $expectedMime . "\n";
    
    // 如果有fileinfo扩展，使用它
    if (extension_loaded('fileinfo')) {
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $detectedMime = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        echo "Detected MIME: " . $detectedMime . "\n";
    } else {
        echo "Detected MIME: skipped (fileinfo not available)\n";
    }
    echo "\n";
    
    // 创建目标目录
    $targetDir = 'simple_fix_uploads/';
    if (!is_dir($targetDir)) {
        writeLog("Creating directory: " . $targetDir);
        if (!mkdir($targetDir, 0755, true)) {
            writeLog("Failed to create directory");
            echo "Error: Failed to create directory\n";
            exit;
        }
        echo "Directory created: " . $targetDir . " ✓\n";
    } else {
        echo "Directory exists: " . $targetDir . " ✓\n";
    }
    
    // 检查目录权限
    echo "Directory writable: " . (is_writable($targetDir) ? 'YES' : 'NO') . "\n\n";
    
    // 生成目标文件名
    $targetFile = $targetDir . 'upload_' . date('YmdHis') . '_' . uniqid() . '.' . $extension;
    writeLog("Target file: " . $targetFile);
    
    echo "Target file: " . $targetFile . "\n";
    
    // 移动文件
    echo "Moving file...\n";
    if (move_uploaded_file($file['tmp_name'], $targetFile)) {
        writeLog("File moved successfully");
        $finalSize = filesize($targetFile);
        writeLog("Final file size: " . $finalSize);
        
        echo "\n🎉 SUCCESS! 🎉\n";
        echo "File uploaded successfully!\n";
        echo "Final location: " . $targetFile . "\n";
        echo "Final size: " . $finalSize . " bytes\n";
        
        // 验证文件内容
        if ($finalSize > 0) {
            echo "File verification: OK ✓\n";
        } else {
            echo "File verification: FAILED (0 bytes)\n";
        }
        
    } else {
        writeLog("Failed to move file");
        echo "\n❌ FAILED ❌\n";
        echo "Failed to move uploaded file\n";
        echo "Check directory permissions and disk space\n";
        
        // 额外的调试信息
        echo "\nDebug Info:\n";
        echo "Source exists: " . (file_exists($file['tmp_name']) ? 'yes' : 'no') . "\n";
        echo "Target dir writable: " . (is_writable($targetDir) ? 'yes' : 'no') . "\n";
        echo "Disk free space: " . round(disk_free_space('.') / 1024 / 1024, 2) . " MB\n";
    }
    
} catch (Exception $e) {
    writeLog("Exception: " . $e->getMessage());
    echo "\nException: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
    
} catch (Error $e) {
    writeLog("Fatal Error: " . $e->getMessage());
    echo "\nFatal Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}

writeLog("=== Simple Upload Fix Test End ===");
?>
