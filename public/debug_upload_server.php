<?php
/**
 * 服务器上传调试脚本
 */
header('Content-Type: application/json; charset=utf-8');

$debug = [];

// 1. 基本环境信息
$debug['environment'] = [
    'php_version' => PHP_VERSION,
    'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
    'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown',
    'script_filename' => $_SERVER['SCRIPT_FILENAME'] ?? 'Unknown',
    'current_dir' => __DIR__,
    'project_root' => dirname(__DIR__),
];

// 2. 目录检查
$uploadPath = dirname(__DIR__) . '/public/base/ico/' . date('Y/m/d');
$debug['directories'] = [
    'upload_path' => $uploadPath,
    'upload_path_exists' => is_dir($uploadPath),
    'upload_path_writable' => is_writable(dirname($uploadPath)),
    'public_dir_exists' => is_dir(dirname(__DIR__) . '/public'),
    'base_dir_exists' => is_dir(dirname(__DIR__) . '/public/base'),
    'ico_dir_exists' => is_dir(dirname(__DIR__) . '/public/base/ico'),
];

// 3. 尝试创建目录
if (!is_dir($uploadPath)) {
    $debug['directory_creation'] = [
        'attempted' => true,
        'success' => mkdir($uploadPath, 0755, true),
        'error' => error_get_last()
    ];
} else {
    $debug['directory_creation'] = [
        'attempted' => false,
        'reason' => 'Directory already exists'
    ];
}

// 4. 权限检查
$debug['permissions'] = [
    'public_readable' => is_readable(dirname(__DIR__) . '/public'),
    'public_writable' => is_writable(dirname(__DIR__) . '/public'),
    'upload_path_readable' => is_readable($uploadPath),
    'upload_path_writable' => is_writable($uploadPath),
];

// 5. 文件上传配置
$debug['upload_config'] = [
    'file_uploads' => ini_get('file_uploads'),
    'upload_max_filesize' => ini_get('upload_max_filesize'),
    'post_max_size' => ini_get('post_max_size'),
    'max_file_uploads' => ini_get('max_file_uploads'),
    'upload_tmp_dir' => ini_get('upload_tmp_dir'),
];

// 6. 如果有文件上传，处理上传
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['file'])) {
    $debug['upload_attempt'] = [
        'file_info' => $_FILES['file'],
        'upload_error_code' => $_FILES['file']['error'],
        'upload_error_message' => [
            UPLOAD_ERR_OK => 'No error',
            UPLOAD_ERR_INI_SIZE => 'File too large (ini)',
            UPLOAD_ERR_FORM_SIZE => 'File too large (form)',
            UPLOAD_ERR_PARTIAL => 'Partial upload',
            UPLOAD_ERR_NO_FILE => 'No file uploaded',
            UPLOAD_ERR_NO_TMP_DIR => 'No temp directory',
            UPLOAD_ERR_CANT_WRITE => 'Cannot write to disk',
            UPLOAD_ERR_EXTENSION => 'Extension stopped upload'
        ][$_FILES['file']['error']] ?? 'Unknown error',
        'tmp_file_exists' => file_exists($_FILES['file']['tmp_name']),
        'tmp_file_readable' => is_readable($_FILES['file']['tmp_name']),
    ];
    
    // 尝试移动文件
    if ($_FILES['file']['error'] === UPLOAD_ERR_OK) {
        $fileName = date('YmdHis') . '_debug_' . $_FILES['file']['name'];
        $targetPath = $uploadPath . '/' . $fileName;
        
        $debug['file_move'] = [
            'target_path' => $targetPath,
            'move_success' => move_uploaded_file($_FILES['file']['tmp_name'], $targetPath),
            'file_exists_after_move' => file_exists($targetPath),
            'file_size_after_move' => file_exists($targetPath) ? filesize($targetPath) : 0,
        ];
        
        if (file_exists($targetPath)) {
            $debug['success_url'] = '/base/ico/' . date('Y/m/d') . '/' . $fileName;
        }
    }
}

// 7. ThinkPHP相关检查
$debug['thinkphp'] = [
    'app_path_defined' => defined('APP_PATH'),
    'app_path_value' => defined('APP_PATH') ? APP_PATH : 'Not defined',
    'thinkphp_dir_exists' => is_dir(dirname(__DIR__) . '/thinkphp'),
    'application_dir_exists' => is_dir(dirname(__DIR__) . '/application'),
];

echo json_encode($debug, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?>
