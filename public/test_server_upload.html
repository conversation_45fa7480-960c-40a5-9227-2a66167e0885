<!DOCTYPE html>
<html>
<head>
    <title>服务器上传测试</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-info { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .error { color: red; }
        .success { color: green; }
        pre { background: #f0f0f0; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>服务器上传调试测试</h1>
    
    <div class="debug-info">
        <h3>步骤1：查看环境信息</h3>
        <button onclick="checkEnvironment()">检查服务器环境</button>
        <div id="envResult"></div>
    </div>
    
    <div class="debug-info">
        <h3>步骤2：测试文件上传</h3>
        <form id="uploadForm" enctype="multipart/form-data">
            <input type="file" name="file" accept="image/*" required>
            <button type="submit">测试上传</button>
        </form>
        <div id="uploadResult"></div>
    </div>
    
    <div class="debug-info">
        <h3>步骤3：测试原始上传接口</h3>
        <form id="originalUploadForm" enctype="multipart/form-data">
            <input type="file" name="file" accept="image/*" required>
            <button type="submit">测试原始接口</button>
        </form>
        <div id="originalResult"></div>
    </div>
    
    <script>
    function checkEnvironment() {
        fetch('/debug_upload_server.php')
        .then(response => response.json())
        .then(data => {
            document.getElementById('envResult').innerHTML = 
                '<h4>环境信息：</h4><pre>' + JSON.stringify(data, null, 2) + '</pre>';
        })
        .catch(error => {
            document.getElementById('envResult').innerHTML = 
                '<p class="error">获取环境信息失败: ' + error.message + '</p>';
        });
    }
    
    document.getElementById('uploadForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData();
        const fileInput = this.querySelector('input[type="file"]');
        formData.append('file', fileInput.files[0]);
        
        fetch('/debug_upload_server.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            document.getElementById('uploadResult').innerHTML = 
                '<h4>上传测试结果：</h4><pre>' + JSON.stringify(data, null, 2) + '</pre>';
            
            if (data.success_url) {
                document.getElementById('uploadResult').innerHTML += 
                    '<p class="success">上传成功！</p>' +
                    '<img src="' + data.success_url + '" style="max-width: 300px;">';
            }
        })
        .catch(error => {
            document.getElementById('uploadResult').innerHTML = 
                '<p class="error">上传测试失败: ' + error.message + '</p>';
        });
    });
    
    document.getElementById('originalUploadForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData();
        const fileInput = this.querySelector('input[type="file"]');
        formData.append('file', fileInput.files[0]);
        
        fetch('/admin/system/upload', {
            method: 'POST',
            body: formData
        })
        .then(response => response.text())
        .then(text => {
            try {
                const data = JSON.parse(text);
                document.getElementById('originalResult').innerHTML = 
                    '<h4>原始接口结果：</h4><pre>' + JSON.stringify(data, null, 2) + '</pre>';
                
                if (data.data) {
                    document.getElementById('originalResult').innerHTML += 
                        '<p class="success">原始接口上传成功！</p>' +
                        '<img src="' + data.data + '" style="max-width: 300px;">';
                }
            } catch (e) {
                document.getElementById('originalResult').innerHTML = 
                    '<h4>原始接口响应（非JSON）：</h4><pre>' + text + '</pre>';
            }
        })
        .catch(error => {
            document.getElementById('originalResult').innerHTML = 
                '<p class="error">原始接口测试失败: ' + error.message + '</p>';
        });
    });
    </script>
</body>
</html>
