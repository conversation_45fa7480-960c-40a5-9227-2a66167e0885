<!DOCTYPE html>
<html>
<head>
    <title>上传测试</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
        button { padding: 10px 20px; margin: 10px 0; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
    </style>
</head>
<body>
    <h1>上传功能测试</h1>
    
    <div class="test-section">
        <h2>1. 基础连接测试</h2>
        <button onclick="testConnection()">测试连接</button>
        <div id="connection-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 简单上传测试</h2>
        <input type="file" id="test-file" accept="image/*">
        <button onclick="testSimpleUpload()">测试简单上传</button>
        <div id="simple-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. Banner上传测试</h2>
        <input type="file" id="banner-file" accept="image/*">
        <button onclick="testBannerUpload()">测试Banner上传</button>
        <div id="banner-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>4. Banner测试接口</h2>
        <button onclick="testBannerTest()">测试Banner测试接口</button>
        <div id="banner-test-result" class="result"></div>
    </div>

    <script>
        function testConnection() {
            fetch('/admin/test/index')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('connection-result').innerHTML = 
                        '<div class="success">连接成功: ' + JSON.stringify(data, null, 2) + '</div>';
                })
                .catch(error => {
                    document.getElementById('connection-result').innerHTML = 
                        '<div class="error">连接失败: ' + error.message + '</div>';
                });
        }
        
        function testSimpleUpload() {
            const fileInput = document.getElementById('test-file');
            if (!fileInput.files[0]) {
                alert('请选择文件');
                return;
            }
            
            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            
            fetch('/admin/test/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                const resultClass = data.code === 200 ? 'success' : 'error';
                document.getElementById('simple-result').innerHTML = 
                    '<div class="' + resultClass + '">结果: ' + JSON.stringify(data, null, 2) + '</div>';
            })
            .catch(error => {
                document.getElementById('simple-result').innerHTML = 
                    '<div class="error">请求失败: ' + error.message + '</div>';
            });
        }
        
        function testBannerUpload() {
            const fileInput = document.getElementById('banner-file');
            if (!fileInput.files[0]) {
                alert('请选择文件');
                return;
            }
            
            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            
            fetch('/admin/banner/doupload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                const resultClass = data.code === 200 ? 'success' : 'error';
                document.getElementById('banner-result').innerHTML = 
                    '<div class="' + resultClass + '">结果: ' + JSON.stringify(data, null, 2) + '</div>';
            })
            .catch(error => {
                document.getElementById('banner-result').innerHTML = 
                    '<div class="error">请求失败: ' + error.message + '</div>';
            });
        }
        
        function testBannerTest() {
            fetch('/admin/banner/testUpload')
                .then(response => response.json())
                .then(data => {
                    const resultClass = data.code === 200 ? 'success' : 'error';
                    document.getElementById('banner-test-result').innerHTML = 
                        '<div class="' + resultClass + '">结果: ' + JSON.stringify(data, null, 2) + '</div>';
                })
                .catch(error => {
                    document.getElementById('banner-test-result').innerHTML = 
                        '<div class="error">请求失败: ' + error.message + '</div>';
                });
        }
    </script>
</body>
</html>
