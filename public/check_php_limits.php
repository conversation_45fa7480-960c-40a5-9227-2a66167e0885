<?php
/**
 * 检查PHP上传限制
 */

header('Content-Type: application/json; charset=utf-8');

function formatBytes($size, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    return round($size, $precision) . ' ' . $units[$i];
}

function parseSize($size) {
    $unit = preg_replace('/[^bkmgtpezy]/i', '', $size);
    $size = preg_replace('/[^0-9\.]/', '', $size);
    if ($unit) {
        return round($size * pow(1024, stripos('bkmgtpezy', $unit[0])));
    } else {
        return round($size);
    }
}

$phpLimits = [
    'upload_max_filesize' => ini_get('upload_max_filesize'),
    'post_max_size' => ini_get('post_max_size'),
    'max_file_uploads' => ini_get('max_file_uploads'),
    'max_execution_time' => ini_get('max_execution_time'),
    'max_input_time' => ini_get('max_input_time'),
    'memory_limit' => ini_get('memory_limit'),
    'file_uploads' => ini_get('file_uploads') ? 'enabled' : 'disabled'
];

$phpLimitsBytes = [
    'upload_max_filesize' => parseSize($phpLimits['upload_max_filesize']),
    'post_max_size' => parseSize($phpLimits['post_max_size']),
    'memory_limit' => parseSize($phpLimits['memory_limit'])
];

$analysis = [];

// 分析上传限制
if ($phpLimitsBytes['upload_max_filesize'] < 10 * 1024 * 1024) {
    $analysis[] = '⚠️ upload_max_filesize (' . formatBytes($phpLimitsBytes['upload_max_filesize']) . ') 小于应用要求的10MB';
} else {
    $analysis[] = '✅ upload_max_filesize (' . formatBytes($phpLimitsBytes['upload_max_filesize']) . ') 满足要求';
}

if ($phpLimitsBytes['post_max_size'] < 10 * 1024 * 1024) {
    $analysis[] = '⚠️ post_max_size (' . formatBytes($phpLimitsBytes['post_max_size']) . ') 小于应用要求的10MB';
} else {
    $analysis[] = '✅ post_max_size (' . formatBytes($phpLimitsBytes['post_max_size']) . ') 满足要求';
}

if ($phpLimits['file_uploads'] !== 'enabled') {
    $analysis[] = '❌ 文件上传被禁用';
} else {
    $analysis[] = '✅ 文件上传已启用';
}

// 检查最小限制
$minLimit = min($phpLimitsBytes['upload_max_filesize'], $phpLimitsBytes['post_max_size']);
$analysis[] = '📏 实际最大上传大小: ' . formatBytes($minLimit);

if ($minLimit < 1024 * 1024) {
    $analysis[] = '❌ 实际限制过小，可能导致大部分图片无法上传';
} elseif ($minLimit < 5 * 1024 * 1024) {
    $analysis[] = '⚠️ 实际限制较小，可能导致高质量图片无法上传';
} else {
    $analysis[] = '✅ 实际限制合理';
}

$result = [
    'status' => 'success',
    'timestamp' => date('Y-m-d H:i:s'),
    'php_limits' => $phpLimits,
    'php_limits_bytes' => $phpLimitsBytes,
    'analysis' => $analysis,
    'recommendations' => []
];

// 提供建议
if ($phpLimitsBytes['upload_max_filesize'] < 10 * 1024 * 1024) {
    $result['recommendations'][] = '建议在php.ini中设置: upload_max_filesize = 10M';
}

if ($phpLimitsBytes['post_max_size'] < 10 * 1024 * 1024) {
    $result['recommendations'][] = '建议在php.ini中设置: post_max_size = 10M';
}

if ($phpLimits['max_execution_time'] < 60) {
    $result['recommendations'][] = '建议在php.ini中设置: max_execution_time = 60';
}

echo json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?>
