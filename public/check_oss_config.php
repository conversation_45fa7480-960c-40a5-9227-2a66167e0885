<?php
/**
 * 检查OSS配置状态
 */

// 设置错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json; charset=utf-8');

try {
    // 引入ThinkPHP框架
    require_once '../thinkphp/start.php';
    
    // 获取SystemModel
    $systemModel = new \app\admin\model\SystemModel();
    
    // 检查数据库中的OSS配置
    $dbOssConfig = $systemModel->getConfig('oss');
    
    // 检查静态配置文件
    $fileOssConfig = \think\Config::get('oss');
    
    // 创建OssModel实例来检查最终配置
    $ossModel = new \app\admin\model\OssModel();
    
    // 使用反射来访问私有方法
    $reflection = new ReflectionClass($ossModel);
    $getOssConfigMethod = $reflection->getMethod('getOssConfig');
    $getOssConfigMethod->setAccessible(true);
    $finalOssConfig = $getOssConfigMethod->invoke($ossModel);
    
    $result = [
        'status' => 'success',
        'timestamp' => date('Y-m-d H:i:s'),
        'database_oss_config' => $dbOssConfig,
        'file_oss_config' => $fileOssConfig,
        'final_oss_config' => $finalOssConfig,
        'oss_enabled' => !empty($finalOssConfig),
        'analysis' => []
    ];
    
    // 分析配置
    if (empty($finalOssConfig)) {
        $result['analysis'][] = '✅ OSS未启用，将使用本地上传';
        $result['upload_mode'] = 'local';
    } else {
        $result['analysis'][] = '⚠️ OSS已启用，将使用OSS上传';
        $result['upload_mode'] = 'oss';
        $result['oss_provider'] = $finalOssConfig['provider'] ?? 'unknown';
        
        if (isset($finalOssConfig['domain']) && !empty($finalOssConfig['domain'])) {
            $result['analysis'][] = '📍 OSS自定义域名: ' . $finalOssConfig['domain'];
        }
    }
    
    // 检查文件类型配置
    $allowedTypes = $fileOssConfig['allowed_types']['image'] ?? ['jpg', 'jpeg', 'png', 'gif'];
    $result['allowed_image_types'] = $allowedTypes;
    $result['png_supported'] = in_array('png', $allowedTypes);
    
    if ($result['png_supported']) {
        $result['analysis'][] = '✅ PNG格式被支持';
    } else {
        $result['analysis'][] = '❌ PNG格式不被支持';
    }
    
    // 检查文件大小限制
    $maxSize = $fileOssConfig['max_size']['image'] ?? (10 * 1024 * 1024);
    $result['max_image_size'] = $maxSize;
    $result['max_image_size_mb'] = round($maxSize / 1024 / 1024, 2);
    $result['analysis'][] = '📏 图片最大大小: ' . $result['max_image_size_mb'] . 'MB';
    
    echo json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
} catch (Error $e) {
    echo json_encode([
        'status' => 'fatal_error',
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
}
?>
