<!DOCTYPE html>
<html>
<head>
    <title>文件上传限制测试</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        button { padding: 10px 20px; margin: 10px 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
        .result { margin: 10px 0; padding: 15px; background: #f5f5f5; border-radius: 3px; white-space: pre-wrap; font-family: monospace; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        .warning { background: #fff3e0; color: #ef6c00; }
        input[type="file"] { margin: 10px 0; }
        h2 { color: #333; }
        .file-info { background: #e3f2fd; padding: 10px; margin: 10px 0; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>📏 文件上传限制测试</h1>
    <p>测试不同大小和格式的文件上传，帮助诊断PNG上传问题。</p>

    <div class="section">
        <h2>文件信息检查</h2>
        <input type="file" id="file-check" accept="image/*" onchange="checkFileInfo()">
        <div id="file-info" class="file-info" style="display:none;"></div>
    </div>

    <div class="section">
        <h2>小文件测试（< 1MB）</h2>
        <p>测试小尺寸的PNG和JPG文件</p>
        <input type="file" id="small-file" accept="image/*">
        <br>
        <button onclick="testUpload('small-file', 'small-result')">🔬 测试小文件上传</button>
        <div id="small-result" class="result" style="display:none;"></div>
    </div>

    <div class="section">
        <h2>中等文件测试（1-5MB）</h2>
        <p>测试中等大小的图片文件</p>
        <input type="file" id="medium-file" accept="image/*">
        <br>
        <button onclick="testUpload('medium-file', 'medium-result')">📊 测试中等文件上传</button>
        <div id="medium-result" class="result" style="display:none;"></div>
    </div>

    <div class="section">
        <h2>大文件测试（5-10MB）</h2>
        <p>测试接近限制大小的文件</p>
        <input type="file" id="large-file" accept="image/*">
        <br>
        <button onclick="testUpload('large-file', 'large-result')">📈 测试大文件上传</button>
        <div id="large-result" class="result" style="display:none;"></div>
    </div>

    <div class="section">
        <h2>超大文件测试（> 10MB）</h2>
        <p>测试超过限制的文件（应该失败）</p>
        <input type="file" id="huge-file" accept="image/*">
        <br>
        <button onclick="testUpload('huge-file', 'huge-result')">⚠️ 测试超大文件上传</button>
        <div id="huge-result" class="result" style="display:none;"></div>
    </div>

    <script>
        function showResult(elementId, content, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = 'result ' + type;
            element.innerHTML = content;
            element.style.display = 'block';
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function checkFileInfo() {
            const fileInput = document.getElementById('file-check');
            const fileInfoDiv = document.getElementById('file-info');
            
            if (fileInput.files.length > 0) {
                const file = fileInput.files[0];
                const info = `
📁 文件信息：
• 文件名: ${file.name}
• 文件大小: ${formatFileSize(file.size)} (${file.size} bytes)
• 文件类型: ${file.type}
• 最后修改: ${new Date(file.lastModified).toLocaleString()}
• 扩展名: ${file.name.split('.').pop().toLowerCase()}

📏 大小分析：
• 是否超过10MB: ${file.size > 10 * 1024 * 1024 ? '❌ 是' : '✅ 否'}
• 是否为PNG: ${file.type === 'image/png' ? '✅ 是' : '❌ 否'}
• 是否为JPG: ${file.type === 'image/jpeg' ? '✅ 是' : '❌ 否'}
                `;
                
                fileInfoDiv.innerHTML = info;
                fileInfoDiv.style.display = 'block';
            } else {
                fileInfoDiv.style.display = 'none';
            }
        }

        function testUpload(fileInputId, resultId) {
            const fileInput = document.getElementById(fileInputId);
            if (!fileInput.files[0]) {
                alert('请选择文件');
                return;
            }
            
            const file = fileInput.files[0];
            showResult(resultId, `🔄 正在上传文件...
文件名: ${file.name}
文件大小: ${formatFileSize(file.size)}
文件类型: ${file.type}`, 'warning');
            
            const formData = new FormData();
            formData.append('file', file);
            
            const startTime = Date.now();
            
            fetch('/admin/banner/doupload', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                const endTime = Date.now();
                const uploadTime = ((endTime - startTime) / 1000).toFixed(2);
                
                console.log('Response status:', response.status);
                return response.text().then(text => ({
                    status: response.status,
                    text: text,
                    uploadTime: uploadTime
                }));
            })
            .then(({status, text, uploadTime}) => {
                console.log('Response text:', text);
                try {
                    const data = JSON.parse(text);
                    const type = data.code === 200 ? 'success' : 'error';
                    const icon = type === 'success' ? '✅' : '❌';
                    
                    const result = `${icon} 上传结果 (${uploadTime}秒):

📁 文件信息:
• 文件名: ${file.name}
• 文件大小: ${formatFileSize(file.size)}
• 文件类型: ${file.type}

📡 服务器响应:
${JSON.stringify(data, null, 2)}`;
                    
                    showResult(resultId, result, type);
                } catch (e) {
                    showResult(resultId, `❌ 响应解析失败 (${uploadTime}秒):

📁 文件信息:
• 文件名: ${file.name}
• 文件大小: ${formatFileSize(file.size)}
• 文件类型: ${file.type}

📡 原始响应:
${text}`, 'error');
                }
            })
            .catch(error => {
                showResult(resultId, `❌ 请求失败:

📁 文件信息:
• 文件名: ${file.name}
• 文件大小: ${formatFileSize(file.size)}
• 文件类型: ${file.type}

❌ 错误信息:
${error.message}`, 'error');
            });
        }
    </script>
</body>
</html>
