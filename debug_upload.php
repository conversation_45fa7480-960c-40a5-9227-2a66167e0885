<?php
/**
 * 调试上传功能
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 模拟ThinkPHP环境
define('APP_PATH', __DIR__ . '/application/');
define('ROOT_PATH', __DIR__ . '/');

// 检查composer自动加载
if (file_exists('vendor/autoload.php')) {
    require_once 'vendor/autoload.php';
}

// 检查ThinkPHP基础文件
if (file_exists('thinkphp/base.php')) {
    require_once 'thinkphp/base.php';
}

// 加载公共函数
if (file_exists('application/common.php')) {
    require_once 'application/common.php';
}

echo "=== 上传功能调试 ===\n\n";

// 1. 测试json_exit函数
echo "1. 测试json_exit函数:\n";
if (function_exists('json_exit')) {
    echo "  ✓ json_exit函数存在\n";
    
    // 测试函数调用（捕获输出）
    ob_start();
    try {
        json_exit(200, "测试成功", ["test" => "data"]);
    } catch (Exception $e) {
        echo "  ✗ json_exit调用失败: " . $e->getMessage() . "\n";
    }
    $output = ob_get_clean();
    
    if (!empty($output)) {
        echo "  ✓ json_exit输出正常\n";
        echo "  输出内容: " . substr($output, 0, 100) . "...\n";
    } else {
        echo "  ✗ json_exit无输出\n";
    }
} else {
    echo "  ✗ json_exit函数不存在\n";
}

// 2. 测试路径处理逻辑
echo "\n2. 测试路径处理逻辑:\n";
$testPaths = [
    "lottery/banner/20250827/",
    "public/lottery/banner/20250827/",
    "/lottery/banner/20250827/",
    "base/ico/20250827/"
];

foreach ($testPaths as $originalPath) {
    echo "  原始路径: $originalPath\n";
    
    // 模拟OssModel中的路径处理
    if (strpos($originalPath, 'public/') !== 0) {
        $processedPath = 'public/' . ltrim($originalPath, '/');
    } else {
        $processedPath = $originalPath;
    }
    echo "    处理后: $processedPath\n";
    
    // 模拟URL生成
    $urlPath = str_replace('public/', '', $processedPath);
    $relativePath = '/' . trim($urlPath, '/') . '/test.jpg';
    echo "    URL路径: $relativePath\n";
    
    // 检查目录是否可以创建
    if (!is_dir($processedPath)) {
        if (mkdir($processedPath, 0755, true)) {
            echo "    ✓ 目录创建成功\n";
        } else {
            echo "    ✗ 目录创建失败\n";
        }
    } else {
        echo "    ✓ 目录已存在\n";
    }
    echo "\n";
}

// 3. 测试文件扩展名检查
echo "3. 测试文件扩展名检查:\n";
$testFiles = [
    "test.jpg" => true,
    "test.png" => true,
    "test.gif" => true,
    "test.jpeg" => true,
    "test.txt" => false,
    "test.php" => false,
    "test.exe" => false
];

foreach ($testFiles as $filename => $shouldPass) {
    $temp = explode(".", $filename);
    $extension = end($temp);
    $isValid = in_array($extension, array("gif", "jpeg", "jpg", "png"));
    
    $status = $isValid ? "✓ 通过" : "✗ 拒绝";
    $expected = $shouldPass ? "应通过" : "应拒绝";
    $result = ($isValid === $shouldPass) ? "正确" : "错误";
    
    echo "  $filename ($extension): $status ($expected) - $result\n";
}

// 4. 检查关键类是否可以加载
echo "\n4. 检查类加载:\n";
$classes = [
    'think\\Model',
    'think\\Controller',
    'think\\facade\\Config',
    'think\\facade\\Session'
];

foreach ($classes as $className) {
    if (class_exists($className)) {
        echo "  ✓ $className 可以加载\n";
    } else {
        echo "  ✗ $className 无法加载\n";
    }
}

// 5. 检查OSS相关类
echo "\n5. 检查OSS相关类:\n";
$ossClasses = [
    'OSS\\OssClient',
    'OSS\\Core\\OssException'
];

foreach ($ossClasses as $className) {
    if (class_exists($className)) {
        echo "  ✓ $className 可以加载\n";
    } else {
        echo "  ✗ $className 无法加载 (需要安装OSS SDK)\n";
    }
}

// 6. 生成调试建议
echo "\n=== 调试建议 ===\n";
echo "如果仍然出现500错误，请检查:\n";
echo "1. 服务器错误日志 (通常在 /var/log/apache2/error.log 或 /var/log/nginx/error.log)\n";
echo "2. ThinkPHP运行时日志 (runtime/log/ 目录)\n";
echo "3. PHP错误日志\n";
echo "4. 确保所有必需的PHP扩展已安装\n";
echo "5. 检查文件和目录权限\n";
echo "6. 确保OSS SDK已正确安装 (如果使用OSS上传)\n";

echo "\n调试完成！\n";
