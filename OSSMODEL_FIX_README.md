# OssModel 修复说明

## 修复概述

本次修复解决了后台所有上传接口无法正常工作的问题，主要涉及以下几个方面：

## 主要修复内容

### 1. 类名统一
- **问题**: 类定义为 `OSSModel`（全大写），但实例化时使用 `OssModel`
- **修复**: 统一类名为 `OssModel`

### 2. 配置获取统一
- **问题**: 不同上传方法使用不同的配置获取方式
- **修复**: 所有方法都使用 `getOssConfig()` 方法获取配置

### 3. OSS上传支持
- **问题**: `doupload`、`upload_img`、`upload_xf` 等方法不支持OSS上传
- **修复**: 所有上传方法都优先检查OSS配置，支持OSS上传

### 4. 错误处理统一
- **问题**: 使用 `exit(json_encode())` 方式处理错误
- **修复**: 统一使用 `json_exit()` 函数

### 5. SDK引用修复
- **问题**: 错误的OSS SDK引用路径和类名
- **修复**: 移除错误的require路径，使用正确的类名

## 修复后的功能

### 支持的上传方法
1. `uploadFile($path, $type)` - 通用文件上传
2. `doupload($ImgPath)` - 图片上传（向后兼容）
3. `upload_img($ImgPath)` - 图片上传
4. `upload_xf($ImgPath)` - 图片上传（特殊用途）
5. `upload_video($videoPath)` - 视频上传

### 上传策略
- **优先使用OSS**: 如果配置了OSS且启用，优先使用OSS上传
- **本地回退**: OSS不可用时自动回退到本地上传
- **错误处理**: 统一的错误处理和响应格式

## 配置说明

### OSS配置方式
1. **数据库配置**: 通过后台OSS配置页面设置（推荐）
2. **文件配置**: 修改 `config/oss.php` 文件

### 配置示例
```php
// 数据库配置（通过后台设置）
[
    'enable' => true,
    'provider' => 'aliyun',
    'aliyun' => [
        'access_key_id' => 'your_access_key_id',
        'access_key_secret' => 'your_access_key_secret',
        'endpoint' => 'https://oss-cn-hangzhou.aliyuncs.com',
        'bucket' => 'your_bucket_name',
        'domain' => 'your_custom_domain.com' // 可选
    ]
]
```

## 使用方法

### 在控制器中使用
```php
// 图片上传
public function doupload(){
    $this->OssModel->upload_img("base/ico/");
}

// 视频上传
public function doVideoUpload(){
    $this->OssModel->upload_video("video/files");
}

// 通用文件上传
public function fileUpload(){
    $this->OssModel->uploadFile("uploads/files/", "image");
}
```

### 前端调用
```javascript
// 使用layui upload组件
upload.render({
    elem: '#upload',
    url: '/admin/system/doupload',
    done: function(res){
        if(res.code === 200){
            console.log('上传成功:', res.data);
        }
    }
});
```

## 测试建议

1. **配置OSS参数**: 在后台OSS配置页面设置正确的参数
2. **测试图片上传**: 测试各个模块的图片上传功能
3. **测试视频上传**: 测试视频上传功能
4. **测试本地回退**: 禁用OSS后测试本地上传是否正常
5. **检查文件访问**: 确认上传的文件可以正常访问

## 注意事项

1. **OSS SDK**: 确保已安装阿里云OSS SDK
2. **权限设置**: 确保上传目录有写入权限
3. **文件大小**: 注意配置文件大小限制
4. **域名配置**: 如果使用自定义域名，确保配置正确

## 故障排除

### 常见问题
1. **类未找到**: 检查类名是否正确，确保为 `OssModel`
2. **配置无效**: 检查OSS配置是否正确设置
3. **上传失败**: 检查网络连接和OSS权限
4. **文件无法访问**: 检查域名配置和文件权限

### 调试方法
- 查看错误日志
- 使用浏览器开发者工具检查网络请求
- 测试OSS连接是否正常

## 更新日志

- **2025-08-27**: 完成OssModel修复，支持统一的OSS上传功能
