# Banner上传500错误调试指南

## 当前状态
- ✅ 已简化Banner控制器的doupload方法
- ✅ 添加了详细的调试日志记录
- ✅ 创建了测试页面
- ✅ 确认目录结构正确

## 调试步骤

### 1. 使用测试页面
访问: `https://jsdao.cc/test_banner_simple.html`

这个页面可以：
- 测试Banner上传功能
- 查看详细的调试日志

### 2. 检查调试日志
上传测试后，点击"查看调试日志"按钮，或直接访问：
`https://jsdao.cc/debug_banner_upload.log`

日志会记录：
- 请求时间和方法
- PHP版本信息
- $_FILES数组内容
- 每个检查步骤的结果
- 目录创建和权限信息
- 文件移动过程

### 3. 可能的问题和解决方案

#### 问题1: 500错误且无日志
**原因**: PHP语法错误或致命错误
**解决**: 
```bash
# 检查PHP语法
php -l application/admin/controller/Banner.php

# 查看PHP错误日志
tail -f /var/log/php/error.log
```

#### 问题2: 权限问题
**原因**: 目录无写入权限
**解决**:
```bash
chmod -R 755 public/lottery/banner/
chown -R www-data:www-data public/lottery/banner/
```

#### 问题3: 文件上传配置
**原因**: PHP上传配置限制
**解决**: 检查php.ini设置
```ini
upload_max_filesize = 2048M
post_max_size = 2048M
max_execution_time = 300
memory_limit = 512M
file_uploads = On
```

#### 问题4: Web服务器配置
**原因**: Nginx/Apache配置问题
**解决**: 检查服务器错误日志
```bash
# Nginx
tail -f /var/log/nginx/error.log

# Apache
tail -f /var/log/apache2/error.log
```

### 4. 手动测试命令

#### 测试目录权限
```bash
# 检查目录是否存在和可写
ls -la public/lottery/banner/
touch public/lottery/banner/test.txt
rm public/lottery/banner/test.txt
```

#### 测试PHP文件上传
```bash
# 创建简单的上传测试
curl -X POST \
  https://jsdao.cc/admin/banner/doupload \
  -F "file=@test.jpg"
```

### 5. 当前doupload方法特点

- ✅ 不依赖ThinkPHP的复杂功能
- ✅ 直接使用原生PHP函数
- ✅ 详细的错误日志记录
- ✅ 每个步骤都有检查和记录
- ✅ 使用标准的move_uploaded_file函数

### 6. 预期的成功流程

1. 检查json_exit函数存在
2. 验证$_FILES['file']存在
3. 检查上传错误码
4. 验证文件类型
5. 创建/检查上传目录
6. 生成唯一文件名
7. 移动文件到目标位置
8. 返回成功响应

### 7. 如果仍然500错误

1. **查看服务器错误日志**
2. **检查PHP版本兼容性**
3. **确认Web服务器配置**
4. **检查文件系统权限**
5. **验证ThinkPHP路由配置**

### 8. 联系信息

如果问题仍然存在，请提供：
- 调试日志内容 (`debug_banner_upload.log`)
- 服务器错误日志
- PHP版本和配置信息
- Web服务器类型和版本
