<?php
/**
 * 测试Banner上传功能修复
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== Banner上传功能测试 ===\n\n";

// 1. 检查文件存在性
echo "1. 检查文件存在性:\n";
$files = [
    'application/admin/controller/Banner.php',
    'application/admin/model/OssModel.php',
    'application/common.php'
];

foreach ($files as $file) {
    echo "  $file: " . (file_exists($file) ? "✓ 存在" : "✗ 不存在") . "\n";
}

// 2. 检查目录权限
echo "\n2. 检查目录权限:\n";
$testPath = "public/lottery/banner/" . date('Ymd') . "/";
echo "  测试路径: $testPath\n";

// 创建测试目录
if (!is_dir($testPath)) {
    if (mkdir($testPath, 0755, true)) {
        echo "  ✓ 目录创建成功\n";
    } else {
        echo "  ✗ 目录创建失败\n";
    }
} else {
    echo "  ✓ 目录已存在\n";
}

// 检查目录权限
if (is_writable($testPath)) {
    echo "  ✓ 目录可写\n";
} else {
    echo "  ✗ 目录不可写\n";
}

// 3. 检查Banner控制器的doupload方法
echo "\n3. 检查Banner控制器:\n";
$bannerContent = file_get_contents('application/admin/controller/Banner.php');

// 检查是否有文件检查
if (strpos($bannerContent, 'request()->file(\'file\')') !== false) {
    echo "  ✓ 包含文件检查\n";
} else {
    echo "  ✗ 缺少文件检查\n";
}

// 检查是否有OssModel检查
if (strpos($bannerContent, 'if (!$this->OssModel)') !== false) {
    echo "  ✓ 包含OssModel检查\n";
} else {
    echo "  ✗ 缺少OssModel检查\n";
}

// 检查错误处理
if (strpos($bannerContent, 'catch (\Error $e)') !== false) {
    echo "  ✓ 包含致命错误处理\n";
} else {
    echo "  ✗ 缺少致命错误处理\n";
}

// 4. 检查OssModel的doupload方法
echo "\n4. 检查OssModel:\n";
$ossContent = file_get_contents('application/admin/model/OssModel.php');

// 检查路径处理
if (strpos($ossContent, 'public/') !== false && strpos($ossContent, 'ltrim($ImgPath') !== false) {
    echo "  ✓ 包含正确的路径处理\n";
} else {
    echo "  ✗ 路径处理可能有问题\n";
}

// 检查目录创建
if (strpos($ossContent, 'mkdir($path, 0755, true)') !== false) {
    echo "  ✓ 包含目录创建逻辑\n";
} else {
    echo "  ✗ 缺少目录创建逻辑\n";
}

// 检查URL路径生成
if (strpos($ossContent, 'str_replace(\'public/\', \'\', $path)') !== false) {
    echo "  ✓ 包含正确的URL路径生成\n";
} else {
    echo "  ✗ URL路径生成可能有问题\n";
}

// 5. 检查common.php中的json_exit函数
echo "\n5. 检查公共函数:\n";
$commonContent = file_get_contents('application/common.php');

if (strpos($commonContent, 'function json_exit') !== false) {
    echo "  ✓ json_exit函数存在\n";
} else {
    echo "  ✗ json_exit函数不存在\n";
}

// 6. 模拟路径处理逻辑
echo "\n6. 模拟路径处理:\n";
$originalPath = "lottery/banner/" . date('Ymd') . "/";
echo "  原始路径: $originalPath\n";

// 模拟OssModel中的路径处理逻辑
if (strpos($originalPath, 'public/') !== 0) {
    $processedPath = 'public/' . ltrim($originalPath, '/');
} else {
    $processedPath = $originalPath;
}
echo "  处理后路径: $processedPath\n";

// 模拟URL路径生成
$urlPath = str_replace('public/', '', $processedPath);
$testFileName = 'test.jpg';
$relativePath = '/' . trim($urlPath, '/') . '/' . $testFileName;
echo "  生成的URL路径: $relativePath\n";

// 7. 生成修复总结
echo "\n=== 修复总结 ===\n";
echo "主要修复内容:\n";
echo "1. ✓ Banner控制器增加文件和OssModel检查\n";
echo "2. ✓ Banner控制器增加致命错误处理\n";
echo "3. ✓ OssModel的doupload方法增加路径处理\n";
echo "4. ✓ OssModel增加目录创建逻辑\n";
echo "5. ✓ OssModel修复URL路径生成\n";
echo "6. ✓ 增加详细的调试日志\n";

echo "\n=== 测试建议 ===\n";
echo "1. 确保public目录有写入权限\n";
echo "2. 测试上传小图片文件\n";
echo "3. 检查生成的文件路径是否正确\n";
echo "4. 查看错误日志获取详细信息\n";

echo "\n测试完成！\n";

// 清理测试目录（可选）
// rmdir($testPath);
