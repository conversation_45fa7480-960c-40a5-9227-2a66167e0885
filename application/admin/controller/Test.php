<?php
namespace app\admin\controller;

class Test
{
    public function index()
    {
        header('Content-Type: application/json');
        echo json_encode([
            'code' => 200,
            'msg' => '测试成功',
            'data' => [
                'php_version' => PHP_VERSION,
                'time' => date('Y-m-d H:i:s'),
                'method' => $_SERVER['REQUEST_METHOD'] ?? 'unknown'
            ]
        ]);
        exit;
    }
    
    public function upload()
    {
        header('Content-Type: application/json');
        
        // 检查是否有文件上传
        if (empty($_FILES['file'])) {
            echo json_encode(['code' => 401, 'msg' => '没有上传文件']);
            exit;
        }
        
        // 检查上传错误
        if ($_FILES['file']['error'] !== UPLOAD_ERR_OK) {
            echo json_encode(['code' => 401, 'msg' => '文件上传错误: ' . $_FILES['file']['error']]);
            exit;
        }
        
        // 检查文件类型
        $fileName = $_FILES['file']['name'];
        $extension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
        if (!in_array($extension, ['jpg', 'jpeg', 'png', 'gif'])) {
            echo json_encode(['code' => 401, 'msg' => '不支持的文件类型: ' . $extension]);
            exit;
        }
        
        // 创建上传目录
        $uploadDir = 'public/test/' . date('Ymd') . '/';
        if (!is_dir($uploadDir)) {
            if (!mkdir($uploadDir, 0755, true)) {
                echo json_encode(['code' => 401, 'msg' => '创建目录失败']);
                exit;
            }
        }
        
        // 生成新文件名
        $newFileName = date('YmdHis') . '_' . uniqid() . '.' . $extension;
        $targetPath = $uploadDir . $newFileName;
        
        // 移动文件
        if (move_uploaded_file($_FILES['file']['tmp_name'], $targetPath)) {
            $relativePath = '/test/' . date('Ymd') . '/' . $newFileName;
            echo json_encode(['code' => 200, 'msg' => '上传成功', 'data' => $relativePath]);
        } else {
            echo json_encode(['code' => 401, 'msg' => '文件移动失败']);
        }
        exit;
    }
}
