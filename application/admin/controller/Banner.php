<?php
namespace app\admin\controller;
use think\Request;
use think\Controller;
use app\admin\controller\Base;
class Banner extends Base
{
    public function index()
    {
       return $this->fetch();
    }
    public function list(){
        $get['page'] = $this->request->get('page') ?: 1;
        $get['limit'] = $this->request->get('limit') ?: 10;
        $get['name'] = $this->request->get('name') ?: "";
        $get['cid'] = $this->request->get('cid') ?: "";
        $get['key'] = $this->request->get('key') ?: "";
        $get['status'] = $this->request->get('status') === "" ? "":$this->request->get('status');
        $get['start_time'] = $this->request->get('start_time') ?: "";
        $get['end_time'] = $this->request->get('end_time') ?: "";
        $list = $this->BannerModel->getList($get);
        
        return json_table(0, "",$list['count'],$list['data']);
    }
    public function doupload(){
        // 最基础的错误检查
        if (!function_exists('json_exit')) {
            header('Content-Type: application/json');
            echo json_encode(['code' => 500, 'msg' => 'json_exit函数不存在']);
            exit;
        }

        try {
            // 记录开始调试信息
            error_log('Banner doupload started');

            // 检查基础环境
            if (!isset($this->OssModel)) {
                error_log('OssModel not initialized in Banner controller');
                json_exit(500, "OssModel未初始化");
            }

            // 检查文件是否上传
            if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
                error_log('File upload error: ' . ($_FILES['file']['error'] ?? 'No file'));
                json_exit(401, '文件上传失败');
            }

            $file = request()->file('file');
            if (!$file) {
                error_log('ThinkPHP file object creation failed');
                json_exit(401, '文件对象创建失败');
            }

            // 添加日期目录
            $path = "lottery/banner/" . date('Ymd') . "/";
            error_log('Upload path: ' . $path);

            // 调用OssModel的doupload方法
            error_log('Calling OssModel->doupload');
            $this->OssModel->doupload($path);

        } catch (\Throwable $e) {
            // 捕获所有类型的错误
            $errorMsg = 'Banner Upload Error: ' . $e->getMessage() . ' in ' . $e->getFile() . ':' . $e->getLine();
            error_log($errorMsg);
            error_log('Stack trace: ' . $e->getTraceAsString());

            json_exit(500, "上传失败：" . $e->getMessage());
        }
    }
    public function operation($operation = null, $id = null){
        if(!empty($operation)){
            if($operation == "add"){
   
            } else {
                if(!empty($id) || $id == 0){
                   $info = $this->BannerModel->getOneData($id);
                   $this->assign('info',$info);
                }else{
                   $this->error('编辑错误ID不能为空！'); 
                }
            }
            // $class = $this->MemberModel->selectList();
            // $this->assign('class',$class);              
            $this->assign('operation',$operation);
            return $this->fetch();
        } else {
            $this->error('操作类型错误！');
        }
    }
    public function doSave(){
        $post = $this->request->param();
        $this->BannerModel->saveData($post);
    }
    public function doEditHot(){
        $post = $this->request->param();
        $this->BannerModel->editStatus($post);
    }    
    public function doEditStatus(){
        $post = $this->request->param();
        $this->BannerModel->editStatus($post);
    }  
    
    
     
    // 删除
    public function doDel(){
        $post = $this->request->param();
        $this->BannerModel->delData($post['id']);
    }

    // 测试接口 - 检查基础功能
    public function testUpload(){
        try {
            error_log('Banner testUpload called');

            // 检查基础环境
            $checks = [
                'json_exit_exists' => function_exists('json_exit'),
                'ossmodel_exists' => isset($this->OssModel),
                'ossmodel_class' => isset($this->OssModel) ? get_class($this->OssModel) : 'not set',
                'files_uploaded' => !empty($_FILES),
                'post_method' => $_SERVER['REQUEST_METHOD'] === 'POST',
                'php_version' => PHP_VERSION,
                'upload_max_filesize' => ini_get('upload_max_filesize'),
                'post_max_size' => ini_get('post_max_size'),
                'file_uploads' => ini_get('file_uploads') ? 'enabled' : 'disabled'
            ];

            error_log('Banner test checks: ' . json_encode($checks));
            json_exit(200, '测试成功', $checks);

        } catch (\Throwable $e) {
            error_log('Banner test error: ' . $e->getMessage());
            json_exit(500, '测试失败：' . $e->getMessage());
        }
    }

}
