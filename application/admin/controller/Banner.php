<?php
namespace app\admin\controller;
use think\Request;
use think\Controller;
use app\admin\controller\Base;
class Banner extends Base
{
    public function index()
    {
       return $this->fetch();
    }
    public function list(){
        $get['page'] = $this->request->get('page') ?: 1;
        $get['limit'] = $this->request->get('limit') ?: 10;
        $get['name'] = $this->request->get('name') ?: "";
        $get['cid'] = $this->request->get('cid') ?: "";
        $get['key'] = $this->request->get('key') ?: "";
        $get['status'] = $this->request->get('status') === "" ? "":$this->request->get('status');
        $get['start_time'] = $this->request->get('start_time') ?: "";
        $get['end_time'] = $this->request->get('end_time') ?: "";
        $list = $this->BannerModel->getList($get);
        
        return json_table(0, "",$list['count'],$list['data']);
    }
    public function doupload(){
        // 记录调试信息到文件
        $debugFile = 'public/debug_banner_upload.log';
        $debugInfo = [];

        try {
            $debugInfo[] = "=== Banner Upload Debug Start ===";
            $debugInfo[] = "Time: " . date('Y-m-d H:i:s');
            $debugInfo[] = "Request Method: " . ($_SERVER['REQUEST_METHOD'] ?? 'unknown');
            $debugInfo[] = "PHP Version: " . PHP_VERSION;

            // 直接输出，不依赖任何其他函数
            header('Content-Type: application/json');

            // 检查$_FILES
            $debugInfo[] = "FILES array: " . json_encode($_FILES);

            // 最基础的检查
            if (!function_exists('json_exit')) {
                $debugInfo[] = "ERROR: json_exit函数不存在";
                file_put_contents($debugFile, implode("\n", $debugInfo) . "\n", FILE_APPEND);
                echo json_encode(['code' => 500, 'msg' => 'json_exit函数不存在']);
                exit;
            }
            $debugInfo[] = "json_exit函数存在";

            // 检查是否有文件上传
            if (empty($_FILES['file'])) {
                $debugInfo[] = "ERROR: 没有上传文件";
                file_put_contents($debugFile, implode("\n", $debugInfo) . "\n", FILE_APPEND);
                echo json_encode(['code' => 401, 'msg' => '没有上传文件']);
                exit;
            }
            $debugInfo[] = "文件上传检查通过";

            // 检查上传错误
            if ($_FILES['file']['error'] !== UPLOAD_ERR_OK) {
                $debugInfo[] = "ERROR: 文件上传错误: " . $_FILES['file']['error'];
                file_put_contents($debugFile, implode("\n", $debugInfo) . "\n", FILE_APPEND);
                echo json_encode(['code' => 401, 'msg' => '文件上传错误: ' . $_FILES['file']['error']]);
                exit;
            }
            $debugInfo[] = "文件上传错误检查通过";

            // 检查文件类型
            $fileName = $_FILES['file']['name'];
            $extension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
            $debugInfo[] = "文件名: " . $fileName;
            $debugInfo[] = "文件扩展名: " . $extension;

            if (!in_array($extension, ['jpg', 'jpeg', 'png', 'gif'])) {
                $debugInfo[] = "ERROR: 不支持的文件类型: " . $extension;
                file_put_contents($debugFile, implode("\n", $debugInfo) . "\n", FILE_APPEND);
                echo json_encode(['code' => 401, 'msg' => '不支持的文件类型: ' . $extension]);
                exit;
            }
            $debugInfo[] = "文件类型检查通过";

            // 创建上传目录
            $uploadDir = 'public/lottery/banner/' . date('Ymd') . '/';
            $debugInfo[] = "上传目录: " . $uploadDir;
            $debugInfo[] = "目录是否存在: " . (is_dir($uploadDir) ? 'yes' : 'no');

            if (!is_dir($uploadDir)) {
                if (!mkdir($uploadDir, 0755, true)) {
                    $debugInfo[] = "ERROR: 创建目录失败";
                    file_put_contents($debugFile, implode("\n", $debugInfo) . "\n", FILE_APPEND);
                    echo json_encode(['code' => 401, 'msg' => '创建目录失败']);
                    exit;
                }
                $debugInfo[] = "目录创建成功";
            } else {
                $debugInfo[] = "目录已存在";
            }

            // 检查目录权限
            $debugInfo[] = "目录可写: " . (is_writable($uploadDir) ? 'yes' : 'no');

            // 生成新文件名
            $newFileName = date('YmdHis') . '_' . uniqid() . '.' . $extension;
            $targetPath = $uploadDir . $newFileName;
            $debugInfo[] = "新文件名: " . $newFileName;
            $debugInfo[] = "目标路径: " . $targetPath;
            $debugInfo[] = "临时文件: " . $_FILES['file']['tmp_name'];
            $debugInfo[] = "临时文件存在: " . (file_exists($_FILES['file']['tmp_name']) ? 'yes' : 'no');

            // 移动文件
            if (move_uploaded_file($_FILES['file']['tmp_name'], $targetPath)) {
                $relativePath = '/lottery/banner/' . date('Ymd') . '/' . $newFileName;
                $debugInfo[] = "文件移动成功";
                $debugInfo[] = "相对路径: " . $relativePath;
                $debugInfo[] = "文件大小: " . filesize($targetPath);

                file_put_contents($debugFile, implode("\n", $debugInfo) . "\n", FILE_APPEND);
                echo json_encode(['code' => 200, 'msg' => '上传成功', 'data' => $relativePath]);
            } else {
                $debugInfo[] = "ERROR: 文件移动失败";
                file_put_contents($debugFile, implode("\n", $debugInfo) . "\n", FILE_APPEND);
                echo json_encode(['code' => 401, 'msg' => '文件移动失败']);
            }

        } catch (Exception $e) {
            $debugInfo[] = "EXCEPTION: " . $e->getMessage();
            $debugInfo[] = "File: " . $e->getFile();
            $debugInfo[] = "Line: " . $e->getLine();
            file_put_contents($debugFile, implode("\n", $debugInfo) . "\n", FILE_APPEND);
            echo json_encode(['code' => 500, 'msg' => '发生异常: ' . $e->getMessage()]);
        }

        exit;
    }
    public function operation($operation = null, $id = null){
        if(!empty($operation)){
            if($operation == "add"){
   
            } else {
                if(!empty($id) || $id == 0){
                   $info = $this->BannerModel->getOneData($id);
                   $this->assign('info',$info);
                }else{
                   $this->error('编辑错误ID不能为空！'); 
                }
            }
            // $class = $this->MemberModel->selectList();
            // $this->assign('class',$class);              
            $this->assign('operation',$operation);
            return $this->fetch();
        } else {
            $this->error('操作类型错误！');
        }
    }
    public function doSave(){
        $post = $this->request->param();
        $this->BannerModel->saveData($post);
    }
    public function doEditHot(){
        $post = $this->request->param();
        $this->BannerModel->editStatus($post);
    }    
    public function doEditStatus(){
        $post = $this->request->param();
        $this->BannerModel->editStatus($post);
    }  
    
    
     
    // 删除
    public function doDel(){
        $post = $this->request->param();
        $this->BannerModel->delData($post['id']);
    }

    // 测试接口 - 检查基础功能
    public function testUpload(){
        header('Content-Type: application/json');

        $checks = [
            'status' => 'ok',
            'php_version' => PHP_VERSION,
            'json_exit_exists' => function_exists('json_exit'),
            'upload_max_filesize' => ini_get('upload_max_filesize'),
            'post_max_size' => ini_get('post_max_size'),
            'file_uploads' => ini_get('file_uploads') ? 'enabled' : 'disabled',
            'public_dir_exists' => is_dir('public'),
            'public_dir_writable' => is_writable('public'),
            'request_method' => $_SERVER['REQUEST_METHOD'] ?? 'unknown'
        ];

        echo json_encode(['code' => 200, 'msg' => '测试成功', 'data' => $checks]);
        exit;
    }

}
