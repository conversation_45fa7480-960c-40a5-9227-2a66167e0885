<?php
namespace app\admin\controller;
use think\Request;
use think\Controller;
use app\admin\controller\Base;
class Banner extends Base
{
    public function index()
    {
       return $this->fetch();
    }
    public function list(){
        $get['page'] = $this->request->get('page') ?: 1;
        $get['limit'] = $this->request->get('limit') ?: 10;
        $get['name'] = $this->request->get('name') ?: "";
        $get['cid'] = $this->request->get('cid') ?: "";
        $get['key'] = $this->request->get('key') ?: "";
        $get['status'] = $this->request->get('status') === "" ? "":$this->request->get('status');
        $get['start_time'] = $this->request->get('start_time') ?: "";
        $get['end_time'] = $this->request->get('end_time') ?: "";
        $list = $this->BannerModel->getList($get);
        
        return json_table(0, "",$list['count'],$list['data']);
    }
    public function doupload(){
        // 直接输出，不依赖任何其他函数
        header('Content-Type: application/json');

        // 最基础的检查
        if (!function_exists('json_exit')) {
            echo json_encode(['code' => 500, 'msg' => 'json_exit函数不存在']);
            exit;
        }

        // 检查是否有文件上传
        if (empty($_FILES['file'])) {
            echo json_encode(['code' => 401, 'msg' => '没有上传文件']);
            exit;
        }

        // 检查上传错误
        if ($_FILES['file']['error'] !== UPLOAD_ERR_OK) {
            echo json_encode(['code' => 401, 'msg' => '文件上传错误: ' . $_FILES['file']['error']]);
            exit;
        }

        // 检查文件类型
        $fileName = $_FILES['file']['name'];
        $extension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
        if (!in_array($extension, ['jpg', 'jpeg', 'png', 'gif'])) {
            echo json_encode(['code' => 401, 'msg' => '不支持的文件类型: ' . $extension]);
            exit;
        }

        // 创建上传目录
        $uploadDir = 'public/lottery/banner/' . date('Ymd') . '/';
        if (!is_dir($uploadDir)) {
            if (!mkdir($uploadDir, 0755, true)) {
                echo json_encode(['code' => 401, 'msg' => '创建目录失败']);
                exit;
            }
        }

        // 生成新文件名
        $newFileName = date('YmdHis') . '_' . uniqid() . '.' . $extension;
        $targetPath = $uploadDir . $newFileName;

        // 移动文件
        if (move_uploaded_file($_FILES['file']['tmp_name'], $targetPath)) {
            $relativePath = '/lottery/banner/' . date('Ymd') . '/' . $newFileName;
            echo json_encode(['code' => 200, 'msg' => '上传成功', 'data' => $relativePath]);
        } else {
            echo json_encode(['code' => 401, 'msg' => '文件移动失败']);
        }
        exit;
    }
    public function operation($operation = null, $id = null){
        if(!empty($operation)){
            if($operation == "add"){
   
            } else {
                if(!empty($id) || $id == 0){
                   $info = $this->BannerModel->getOneData($id);
                   $this->assign('info',$info);
                }else{
                   $this->error('编辑错误ID不能为空！'); 
                }
            }
            // $class = $this->MemberModel->selectList();
            // $this->assign('class',$class);              
            $this->assign('operation',$operation);
            return $this->fetch();
        } else {
            $this->error('操作类型错误！');
        }
    }
    public function doSave(){
        $post = $this->request->param();
        $this->BannerModel->saveData($post);
    }
    public function doEditHot(){
        $post = $this->request->param();
        $this->BannerModel->editStatus($post);
    }    
    public function doEditStatus(){
        $post = $this->request->param();
        $this->BannerModel->editStatus($post);
    }  
    
    
     
    // 删除
    public function doDel(){
        $post = $this->request->param();
        $this->BannerModel->delData($post['id']);
    }

    // 测试接口 - 检查基础功能
    public function testUpload(){
        try {
            error_log('Banner testUpload called');

            // 检查基础环境
            $checks = [
                'json_exit_exists' => function_exists('json_exit'),
                'ossmodel_exists' => isset($this->OssModel),
                'ossmodel_class' => isset($this->OssModel) ? get_class($this->OssModel) : 'not set',
                'files_uploaded' => !empty($_FILES),
                'post_method' => $_SERVER['REQUEST_METHOD'] === 'POST',
                'php_version' => PHP_VERSION,
                'upload_max_filesize' => ini_get('upload_max_filesize'),
                'post_max_size' => ini_get('post_max_size'),
                'file_uploads' => ini_get('file_uploads') ? 'enabled' : 'disabled'
            ];

            error_log('Banner test checks: ' . json_encode($checks));
            json_exit(200, '测试成功', $checks);

        } catch (\Throwable $e) {
            error_log('Banner test error: ' . $e->getMessage());
            json_exit(500, '测试失败：' . $e->getMessage());
        }
    }

}
