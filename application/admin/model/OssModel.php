<?php
namespace app\admin\model;
use think\Model;
use think\facade\Session;
use think\facade\Config;
use app\admin\model\UserModel;
use OSS\OssClient;
use OSS\Core\OssException;

class OssModel extends Model
{
    /**
     * 获取OSS配置
     */
    private function getOssConfig()
    {
        // 首先尝试从数据库读取配置
        $systemModel = new \app\admin\model\SystemModel();
        $dbConfig = $systemModel->getConfig('oss');

        if (!empty($dbConfig) && isset($dbConfig['enable']) && $dbConfig['enable']) {
            $provider = $dbConfig['provider'] ?? 'aliyun';
            $providerConfig = $dbConfig[$provider] ?? false;

            if ($providerConfig) {
                // 添加通用配置
                $providerConfig['provider'] = $provider;
                $providerConfig['bucket'] = $providerConfig['bucket'] ?? '';
                return $providerConfig;
            }
        }

        // 如果数据库没有配置，回退到静态配置文件
        $config = Config::get('oss');
        if (!$config || !$config['enable']) {
            return false;
        }

        $provider = $config['provider'] ?? 'aliyun';
        $providerConfig = $config[$provider] ?? false;

        if ($providerConfig) {
            $providerConfig['provider'] = $provider;
        }

        return $providerConfig;
    }

    /**
     * 生成文件名
     */
    private function generateFileName($originalName, $path = '')
    {
        $extension = pathinfo($originalName, PATHINFO_EXTENSION);
        $fileName = date('YmdHis') . uniqid() . '.' . $extension;
        return $path . $fileName;
    }

    /**
     * 根据文件扩展名获取Content-Type
     */
    private function getContentType($extension, $type = 'image')
    {
        $mimeTypes = [
            // 视频类型
            'mp4' => 'video/mp4',
            'avi' => 'video/x-msvideo',
            'mov' => 'video/quicktime',
            'wmv' => 'video/x-ms-wmv',
            'flv' => 'video/x-flv',
            'mkv' => 'video/x-matroska',
            'webm' => 'video/webm',
            'm4v' => 'video/x-m4v',
            '3gp' => 'video/3gpp',

            // 图片类型
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'bmp' => 'image/bmp',
            'webp' => 'image/webp',
            'svg' => 'image/svg+xml',

            // 音频类型
            'mp3' => 'audio/mpeg',
            'wav' => 'audio/wav',
            'ogg' => 'audio/ogg',
            'aac' => 'audio/aac',

            // 文档类型
            'pdf' => 'application/pdf',
            'doc' => 'application/msword',
            'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'xls' => 'application/vnd.ms-excel',
            'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        ];

        return $mimeTypes[$extension] ?? 'application/octet-stream';
    }

    /**
     * OSS上传文件
     */
    public function uploadToOss($file, $path, $type = 'image')
    {
        $ossConfig = $this->getOssConfig();
        if (!$ossConfig) {
            json_exit(401, 'OSS配置未启用或配置错误');
        }

        try {
            $ossClient = new OssClient(
                $ossConfig['access_key_id'],
                $ossConfig['access_key_secret'],
                $ossConfig['endpoint']
            );

            $fileName = $this->generateFileName($file->getInfo()['name'], $path);

            // 根据文件类型设置Content-Type
            $extension = strtolower(pathinfo($file->getInfo()['name'], PATHINFO_EXTENSION));
            $contentType = $this->getContentType($extension, $type);

            // 根据文件类型设置不同的Content-Disposition
            $contentDisposition = 'inline'; // 默认为inline，支持网页播放
            if ($type === 'video') {
                $contentDisposition = 'inline'; // 视频文件设置为inline，支持网页播放
            }

            $result = $ossClient->uploadFile(
                $ossConfig['bucket'],
                $fileName,
                $file->getInfo()['tmp_name'],
                [
                    OssClient::OSS_HEADERS => [
                        'Content-Type' => $contentType,
                        'Content-Disposition' => $contentDisposition,
                        'x-oss-object-acl' => 'public-read',
                        'Cache-Control' => 'max-age=31536000' // 缓存一年
                    ]
                ]
            );

            if (isset($result['info']['http_code']) && $result['info']['http_code'] == 200) {
                // 构建完整URL
                if (!empty($ossConfig['domain'])) {
                    // 使用自定义域名
                    $domain = $ossConfig['domain'];
                } else {
                    // 使用默认OSS域名格式: https://bucket.endpoint
                    $endpoint = $ossConfig['endpoint'];
                    $bucket = $ossConfig['bucket'];

                    // 确保endpoint以https://开头
                    if (!preg_match('/^https?:\/\//', $endpoint)) {
                        $endpoint = 'https://' . $endpoint;
                    }

                    // 构建默认域名
                    $domain = str_replace('://', '://' . $bucket . '.', $endpoint);
                }

                $fullUrl = rtrim($domain, '/') . '/' . $fileName;

                // 根据文件类型返回不同格式的数据
                if ($type === 'video') {
                    $fileInfo = $file->getInfo();
                    $videoData = [
                        'video_url' => $fullUrl,
                        'duration' => 0, // OSS上传无法获取时长
                        'size' => $fileInfo['size'],
                        'extension' => strtolower(pathinfo($fileInfo['name'], PATHINFO_EXTENSION))
                    ];
                    json_exit(200, "视频上传成功", $videoData);
                } else {
                    json_exit(200, "上传成功", $fullUrl);
                }
            } else {
                json_exit(401, "上传错误");
            }
        } catch (OssException $e) {
            json_exit(401, $e->getMessage());
        }
    }

    /**
     * 通用文件上传方法 (支持OSS和本地)
     */
    public function uploadFile($path, $type = 'image')
    {
        $file = request()->file('file');
        if (!$file) {
            json_exit(401, '未上传文件');
        }

        // 检查OSS配置 - 使用统一的配置获取方法
        $ossConfig = $this->getOssConfig();
        if ($ossConfig) {
            // 使用OSS上传
            return $this->uploadToOss($file, $path, $type);
        } else {
            // 使用本地上传
            return $this->uploadToLocal($file, $path, $type);
        }
    }

    /**
     * 本地文件上传
     */
    public function uploadToLocal($file, $path, $type = 'image')
    {
        // 获取文件信息
        $fileInfo = $file->getInfo();
        $extension = strtolower(pathinfo($fileInfo['name'], PATHINFO_EXTENSION));

        // 验证文件类型
        $ossConfig = Config::get('oss');
        $allowedTypes = $ossConfig['allowed_types'][$type] ?? ['jpg', 'jpeg', 'png', 'gif'];

        if (!in_array($extension, $allowedTypes)) {
            json_exit(401, '文件类型不支持，支持格式：' . implode(', ', $allowedTypes));
        }

        // 验证文件大小
        $maxSize = $ossConfig['max_size'][$type] ?? (10 * 1024 * 1024);
        if ($fileInfo['size'] > $maxSize) {
            json_exit(401, '文件过大，最大支持：' . $this->formatBytes($maxSize));
        }

        // 确保路径以public/开头
        if (strpos($path, 'public/') !== 0) {
            $path = 'public/' . ltrim($path, '/');
        }

        // 调试：记录移动前的信息
        $beforeMoveDebug = [
            'target_path' => $path,
            'target_path_exists' => is_dir($path),
            'target_path_writable' => is_writable(dirname($path)),
            'file_tmp_name' => $fileInfo['tmp_name'],
            'file_tmp_exists' => file_exists($fileInfo['tmp_name']),
            'file_original_name' => $fileInfo['name'],
            'file_size' => $fileInfo['size']
        ];
        error_log('Before Move Debug: ' . json_encode($beforeMoveDebug));

        // 移动文件
        $info = $file->move($path);
        if ($info) {
            $fileName = $info->getSaveName();
            $urlPath = str_replace('public/', '', $path);
            $relativePath = '/' . trim($urlPath, '/') . '/' . $fileName;
            $fullFilePath = $path . '/' . $fileName;

            // 调试信息
            $debugInfo = [
                'original_path' => $path,
                'file_name' => $fileName,
                'url_path' => $urlPath,
                'relative_path' => $relativePath,
                'full_file_path' => $fullFilePath,
                'file_exists' => file_exists($fullFilePath),
                'file_size' => file_exists($fullFilePath) ? filesize($fullFilePath) : 0,
                'permissions' => file_exists($fullFilePath) ? substr(sprintf('%o', fileperms($fullFilePath)), -4) : 'N/A',
                'realpath' => realpath($fullFilePath)
            ];
            error_log('Upload Success Debug: ' . json_encode($debugInfo));

            // 如果文件不存在，尝试其他可能的路径
            if (!file_exists($fullFilePath)) {
                $alternativePaths = [
                    $_SERVER['DOCUMENT_ROOT'] . $relativePath,
                    __DIR__ . '/../../public' . $relativePath,
                    dirname(__DIR__, 2) . '/public' . $relativePath
                ];

                foreach ($alternativePaths as $altPath) {
                    if (file_exists($altPath)) {
                        error_log('File found at alternative path: ' . $altPath);
                        break;
                    }
                }
            }

            json_exit(200, "上传成功", $relativePath);
        } else {
            error_log('File move failed: ' . $path . ' - Error: ' . $file->getError());
            json_exit(401, "上传失败：文件移动失败");
        }
    }

    /**
     * 格式化文件大小
     */
    private function formatBytes($size, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');
        for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
            $size /= 1024;
        }
        return round($size, $precision) . ' ' . $units[$i];
    }

        //图片上传本地 (保持向后兼容)
    public function doupload($ImgPath)
    {
        try {
            error_log('OssModel doupload started with path: ' . $ImgPath);

            $file = request()->file('file'); // 获取上传的文件
            if ($file == null) {
                error_log('File object is null');
                json_exit(401, '未上传图片');
            }

            // 检查$_FILES
            if (!isset($_FILES["file"]["name"])) {
                error_log('$_FILES[file][name] not set');
                json_exit(401, '文件信息不完整');
            }

            // 获取文件后缀
            $temp = explode(".", $_FILES["file"]["name"]);
            $extension = strtolower(end($temp)); // 转换为小写
            error_log('File extension: ' . $extension);

            // 判断文件是否合法
            if (!in_array($extension, array("gif", "jpeg", "jpg", "png"))) {
                error_log('Invalid file extension: ' . $extension);
                json_exit(401, '上传图片不合法，支持格式：gif, jpeg, jpg, png');
            }

            // 检查OSS配置，优先使用OSS上传
            error_log('Checking OSS config');
            $ossConfig = $this->getOssConfig();
            if ($ossConfig) {
                error_log('Using OSS upload');
                // 使用OSS上传
                return $this->uploadToOss($file, $ImgPath, 'image');
            }

            error_log('Using local upload');
        } catch (\Throwable $e) {
            error_log('Error in doupload start: ' . $e->getMessage());
            json_exit(500, '上传初始化失败：' . $e->getMessage());
        }

        // 使用本地上传
        try {
            // 确保路径以public/开头
            if (strpos($ImgPath, 'public/') !== 0) {
                $path = 'public/' . ltrim($ImgPath, '/');
            } else {
                $path = $ImgPath;
            }

            error_log('Processed upload path: ' . $path);

            // 确保目录存在
            if (!is_dir($path)) {
                error_log('Creating directory: ' . $path);
                if (!mkdir($path, 0755, true)) {
                    error_log('Failed to create directory: ' . $path);
                    json_exit(401, '创建上传目录失败');
                }
                error_log('Directory created successfully');
            } else {
                error_log('Directory already exists: ' . $path);
            }

            // 检查目录权限
            if (!is_writable($path)) {
                error_log('Directory not writable: ' . $path);
                json_exit(401, '上传目录无写入权限');
            }
        } catch (\Throwable $e) {
            error_log('Error in path processing: ' . $e->getMessage());
            json_exit(500, '路径处理失败：' . $e->getMessage());
        }

        try {
            // 调试信息：记录移动前的状态
            $beforeMoveDebug = [
                'original_path' => $ImgPath,
                'processed_path' => $path,
                'path_exists' => is_dir($path),
                'path_writable' => is_writable($path),
                'file_tmp_name' => $_FILES["file"]["tmp_name"],
                'file_tmp_exists' => file_exists($_FILES["file"]["tmp_name"]),
                'file_size' => $_FILES["file"]["size"]
            ];
            error_log('Doupload Before Move Debug: ' . json_encode($beforeMoveDebug));

            error_log('Starting file move operation');
            $info = $file->move($path); // 移动文件到指定目录 没有则创建

            if ($info) {
                error_log('File move successful');
                $img = $info->getSaveName();
                $fullFilePath = $path . '/' . $img;

                // 生成相对路径URL（浏览器会自动使用当前页面的协议）
                $urlPath = str_replace('public/', '', $path);
                $relativePath = '/' . trim($urlPath, '/') . '/' . $img;

                // 调试信息：记录移动后的状态
                $afterMoveDebug = [
                    'file_name' => $img,
                    'full_file_path' => $fullFilePath,
                    'relative_path' => $relativePath,
                    'file_exists' => file_exists($fullFilePath),
                    'file_size' => file_exists($fullFilePath) ? filesize($fullFilePath) : 0,
                    'realpath' => realpath($fullFilePath)
                ];
                error_log('Doupload After Move Debug: ' . json_encode($afterMoveDebug));

                error_log('Upload completed successfully, returning: ' . $relativePath);
                json_exit(200,"上传成功", $relativePath);
            } else {
                $errorMsg = $file->getError();
                error_log('Doupload file move failed: ' . $path . ' - Error: ' . $errorMsg);
                json_exit(401, "上传失败：文件移动失败 - " . $errorMsg);
            }
        } catch (\Throwable $e) {
            error_log('Error in file move operation: ' . $e->getMessage());
            error_log('Stack trace: ' . $e->getTraceAsString());
            json_exit(500, '文件移动过程中发生错误：' . $e->getMessage());
        }
    }
        //图片上传本地
    public function upload_img($ImgPath)
    {
        // 检查OSS配置，优先使用OSS上传
        $ossConfig = $this->getOssConfig();
        if ($ossConfig) {
            // 使用OSS上传
            $file = request()->file('file');
            if (!$file) {
                json_exit(401, '未上传文件');
            }
            return $this->uploadToOss($file, $ImgPath, 'image');
        } else {
            // 使用本地上传
            return $this->uploadFile($ImgPath, 'image');
        }
    }
    
    public function upload_xf($ImgPath)
    {
        $file = request()->file('file'); // 获取上传的文件
        if ($file == null) {
            json_exit(401, '未上传图片');
        }

        // 获取文件后缀
        $temp = explode(".", $_FILES["file"]["name"]);
        $extension = strtolower(end($temp)); // 转换为小写
        // 判断文件是否合法
        if (!in_array($extension, array("gif", "jpeg", "jpg", "png"))) {
            json_exit(401, '上传图片不合法，支持格式：gif, jpeg, jpg, png');
        }

        // 检查OSS配置，优先使用OSS上传
        $ossConfig = $this->getOssConfig();
        if ($ossConfig) {
            // 使用OSS上传
            return $this->uploadToOss($file, $ImgPath, 'image');
        }

        // 使用本地上传
        $path = $ImgPath;
        $info = $file->move($path); // 移动文件到指定目录 没有则创建

        $img = $info->getSaveName();

        // 生成相对路径URL（浏览器会自动使用当前页面的协议）
        $urlPath = str_replace('public/', '', $path);
        $relativePath = '/' . trim($urlPath, '/') . '/' . $img;

        json_exit(200,"上传成功", $relativePath);
    }

    // 视频上传 (支持OSS和本地)
    public function upload_video($videoPath, $type = "video")
    {
        $file = request()->file('file');
        if (!$file) {
            json_exit(401, '未上传视频');
        }

        // 获取文件信息
        $fileInfo = $file->getInfo();
        $extension = strtolower(pathinfo($fileInfo['name'], PATHINFO_EXTENSION));

        // 验证文件类型
        $staticConfig = Config::get('oss');
        $allowedTypes = $staticConfig['allowed_types']['video'] ?? ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv'];

        if (!in_array($extension, $allowedTypes)) {
            json_exit(401, '视频格式不支持，支持格式：' . implode('、', $allowedTypes));
        }

        // 验证文件大小 - 设置为2GB
        $maxSize = $staticConfig['max_size']['video'] ?? (2 * 1024 * 1024 * 1024); // 2GB
        if ($fileInfo['size'] > $maxSize) {
            json_exit(401, '视频文件过大，最大支持：' . $this->formatBytes($maxSize));
        }

        // 检查OSS配置，优先使用OSS上传
        $ossConfig = $this->getOssConfig();
        if ($ossConfig) {
            // 使用OSS上传视频
            return $this->uploadVideoToOss($file, $videoPath, $fileInfo, $extension, $ossConfig);
        } else {
            // 回退到本地存储
            return $this->uploadVideoToLocal($file, $videoPath, $fileInfo, $extension);
        }
    }



    /**
     * 视频OSS上传
     */
    private function uploadVideoToOss($file, $videoPath, $fileInfo, $extension, $ossConfig)
    {
        try {
            // 创建OSS客户端
            $ossClient = new OssClient(
                $ossConfig['access_key_id'],
                $ossConfig['access_key_secret'],
                $ossConfig['endpoint']
            );

            // 生成文件名
            $fileName = date('Y/m/d/') . date('YmdHis') . uniqid() . '.' . $extension;
            $objectName = rtrim($videoPath, '/') . '/' . $fileName;

            // 上传文件到OSS
            $result = $ossClient->uploadFile($ossConfig['bucket'], $objectName, $fileInfo['tmp_name']);

            if ($result) {
                // 构建访问URL - 使用自定义域名前缀 https://tcyp2.
                if (!empty($ossConfig['domain'])) {
                    // 使用自定义域名
                    $domain = $ossConfig['domain'];
                    // 确保域名以https://开头
                    if (!preg_match('/^https?:\/\//', $domain)) {
                        $domain = 'https://' . $domain;
                    }
                } else {
                    // 使用自定义前缀 https://tcyp2. + OSS域名
                    $endpoint = $ossConfig['endpoint'];
                    // 移除可能存在的协议前缀
                    $endpoint = preg_replace('/^https?:\/\//', '', $endpoint);
                    // 构建带有tcyp2前缀的域名
                    $domain = 'https://tcyp2.' . $endpoint;
                }

                $videoUrl = rtrim($domain, '/') . '/' . ltrim($objectName, '/');

                // 调试日志
                error_log("OSS视频上传URL构建 - Endpoint: {$ossConfig['endpoint']}, Domain: {$domain}, ObjectName: {$objectName}, FinalURL: {$videoUrl}");

                $resultData = [
                    'video_url' => $videoUrl,
                    'duration' => 0, // OSS上传无法直接获取视频时长
                    'size' => $fileInfo['size'],
                    'extension' => $extension,
                    'oss_object' => $objectName, // 保存OSS对象名，便于后续管理
                    'debug_info' => [
                        'original_endpoint' => $ossConfig['endpoint'],
                        'constructed_domain' => $domain,
                        'object_name' => $objectName,
                        'final_url' => $videoUrl
                    ]
                ];

                json_exit(200, "视频上传成功", $resultData);
            } else {
                json_exit(401, "视频上传到OSS失败");
            }
        } catch (OssException $e) {
            // OSS上传失败，回退到本地存储
            error_log("OSS视频上传失败: " . $e->getMessage());
            return $this->uploadVideoToLocal($file, $videoPath, $fileInfo, $extension);
        } catch (\Exception $e) {
            // 其他异常，回退到本地存储
            error_log("视频上传异常: " . $e->getMessage());
            return $this->uploadVideoToLocal($file, $videoPath, $fileInfo, $extension);
        }
    }

    /**
     * 视频本地上传
     */
    private function uploadVideoToLocal($file, $videoPath, $fileInfo, $extension)
    {
        try {
            // 确保路径以public/开头
            if (strpos($videoPath, 'public/') !== 0) {
                $videoPath = 'public/' . ltrim($videoPath, '/');
            }

            // 确保目录存在
            if (!is_dir($videoPath)) {
                if (!mkdir($videoPath, 0755, true)) {
                    json_exit(401, '创建上传目录失败');
                }
            }

            $info = $file->move($videoPath);
            if ($info) {
                $videoName = $info->getSaveName();
                $urlPath = str_replace('public/', '', $videoPath);
                $videoUrl = '/' . trim($urlPath, '/') . '/' . $videoName;

                // 获取视频信息
                $videoFullPath = $videoPath . '/' . $videoName;
                $duration = $this->getVideoDuration($videoFullPath);

                $result = [
                    'video_url' => $videoUrl,
                    'duration' => $duration,
                    'size' => $fileInfo['size'],
                    'extension' => $extension
                ];

                json_exit(200, "视频上传成功", $result);
            } else {
                json_exit(401, "视频上传失败：文件移动失败");
            }
        } catch (\Exception $e) {
            json_exit(401, "视频上传失败：" . $e->getMessage());
        }
    }

    // 获取视频时长（简单实现，需要ffmpeg支持更准确）
    private function getVideoDuration($videoPath)
    {
        // 这里是一个简单的实现，实际项目中建议使用ffmpeg
        // 如果系统安装了ffmpeg，可以使用以下命令
        if (function_exists('shell_exec')) {
            $cmd = "ffprobe -v quiet -show_entries format=duration -of csv=\"p=0\" " . escapeshellarg($videoPath);
            $duration = shell_exec($cmd);
            if ($duration && is_numeric(trim($duration))) {
                return intval(trim($duration));
            }
        }

        // 如果无法获取时长，返回0
        return 0;
    }

}