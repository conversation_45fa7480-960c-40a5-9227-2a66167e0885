# Banner上传接口调试说明

## 问题描述
Banner上传接口 `/admin/banner/doupload` 返回500内部服务器错误。

## 已实施的修复

### 1. Banner控制器修复 (`application/admin/controller/Banner.php`)
- ✅ 添加了详细的错误日志记录
- ✅ 增加了基础环境检查（json_exit函数、OssModel等）
- ✅ 使用 `\Throwable` 捕获所有类型的错误
- ✅ 添加了文件上传状态检查
- ✅ 新增测试接口 `testUpload` 用于基础功能检查

### 2. OssModel修复 (`application/admin/model/OssModel.php`)
- ✅ 添加了详细的步骤日志记录
- ✅ 增加了路径处理的错误检查
- ✅ 添加了目录创建和权限检查
- ✅ 增强了文件移动操作的错误处理
- ✅ 使用 `\Throwable` 捕获所有类型的错误

## 调试步骤

### 第一步：测试基础功能
访问测试接口检查基础环境：
```
POST /admin/banner/testUpload
```

这个接口会返回：
- json_exit函数是否存在
- OssModel是否正确初始化
- PHP配置信息
- 上传相关设置

### 第二步：查看错误日志
检查以下位置的错误日志：
1. **PHP错误日志**: `/var/log/php/error.log` 或 `php.ini` 中配置的位置
2. **Web服务器日志**: 
   - Apache: `/var/log/apache2/error.log`
   - Nginx: `/var/log/nginx/error.log`
3. **ThinkPHP运行时日志**: `runtime/log/` 目录
4. **系统日志**: `/var/log/syslog`

### 第三步：检查关键日志信息
查找以下关键日志：
- `Banner doupload started`
- `OssModel doupload started with path:`
- `File extension:`
- `Using local upload` 或 `Using OSS upload`
- `Processed upload path:`
- `Directory created successfully` 或相关错误
- `Starting file move operation`
- `Upload completed successfully`

## 常见问题排查

### 1. 权限问题
```bash
# 检查public目录权限
ls -la public/
# 确保web服务器用户有写入权限
chown -R www-data:www-data public/
chmod -R 755 public/
```

### 2. PHP配置问题
检查PHP配置：
```bash
php -m | grep fileinfo  # 检查fileinfo扩展
php -i | grep upload     # 检查上传配置
```

### 3. 内存/时间限制
```php
// 在控制器中临时增加限制
ini_set('memory_limit', '256M');
ini_set('max_execution_time', 300);
```

### 4. ThinkPHP版本兼容性
如果使用PHP 8.0+，可能需要：
```bash
# 降级到PHP 7.4
sudo update-alternatives --config php
```

## 测试上传

### 使用curl测试
```bash
curl -X POST \
  https://jsdao.cc/admin/banner/doupload \
  -H 'Content-Type: multipart/form-data' \
  -F 'file=@test.jpg'
```

### 使用Postman测试
1. 方法：POST
2. URL：`https://jsdao.cc/admin/banner/doupload`
3. Body类型：form-data
4. 添加字段：key=`file`, type=`File`, 选择图片文件

## 预期的成功响应
```json
{
    "code": 200,
    "msg": "上传成功",
    "data": "/lottery/banner/20250827/filename.jpg"
}
```

## 如果仍然失败

1. **检查服务器配置**：
   - 确保PHP-FPM正常运行
   - 检查Nginx/Apache配置
   - 确认文件上传大小限制

2. **临时禁用OSS**：
   - 在数据库中将OSS配置的enable设为false
   - 或者在config/oss.php中设置enable为false

3. **简化测试**：
   - 先测试 `/admin/banner/testUpload` 接口
   - 确认基础环境正常后再测试文件上传

4. **联系技术支持**：
   - 提供完整的错误日志
   - 说明服务器环境（PHP版本、操作系统等）
   - 提供测试步骤和结果
